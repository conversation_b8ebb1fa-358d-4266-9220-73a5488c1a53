#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QFrame,
    QMessageBox, QHeaderView, QAbstractItemView, QComboBox,
    QLineEdit, QTextEdit, QDateEdit, QSpinBox, QGroupBox, QGridLayout,
    QTabWidget, QCheckBox, QButtonGroup
)
from PyQt5.QtGui import QFont, QIcon, QColor, QPixmap
from PyQt5.QtCore import Qt, QDate
from datetime import datetime, date
import json
import traceback

# دالة الحصول على مسار قاعدة البيانات الصحيح
def get_db_path():
    """
    الحصول على مسار قاعدة البيانات الصحيح
    يعمل في البيئة العادية وبعد التحزيم
    """
    if getattr(sys, 'frozen', False):
        # البرنامج محزم - البحث في مجلد البرنامج الرئيسي
        application_path = os.path.dirname(sys.executable)
    else:
        # البرنامج يعمل من المصدر
        application_path = os.path.dirname(os.path.abspath(__file__))

    db_path = os.path.join(application_path, 'data.db')

    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(db_path):
        # محاولة البحث في مجلد أعلى
        parent_path = os.path.dirname(application_path)
        alternative_db_path = os.path.join(parent_path, 'data.db')
        if os.path.exists(alternative_db_path):
            return alternative_db_path

    return db_path

class MonthlyDutiesManagementWindow(QMainWindow):
    """نافذة إدارة الواجبات الشهرية"""
    
    def __init__(self, parent=None, db_path="data.db", student_id=None):
        super().__init__(parent)
        print(f"🔍 [DEBUG] تهيئة نافذة الواجبات الشهرية...")
        print(f"🔍 [DEBUG] معرف التلميذ: {student_id}")
        print(f"🔍 [DEBUG] مسار قاعدة البيانات: {db_path}")
        
        self.db_path = db_path
        self.student_id = student_id
        self.student_data = None
        self.editing_duty_id = None  # معرف الواجب قيد التعديل
        
        try:
            print(f"🔍 [DEBUG] بدء إعداد واجهة المستخدم...")
            self.setupUI()
            print(f"✅ [SUCCESS] تم إعداد واجهة المستخدم بنجاح")
            
            print(f"🔍 [DEBUG] إنشاء جدول الواجبات...")
            self.create_duties_table()
            print(f"✅ [SUCCESS] تم إنشاء جدول الواجبات بنجاح")
            
            print(f"🔍 [DEBUG] تحميل بيانات التلميذ...")
            self.load_student_data()
            print(f"✅ [SUCCESS] تم تحميل بيانات التلميذ بنجاح")
            
            print(f"🔍 [DEBUG] تحميل بيانات الأداءات...")
            self.load_duties_data()
            print(f"✅ [SUCCESS] تم تحميل بيانات الأداءات بنجاح")

            # تحميل البيانات الموحدة إذا كان التبويب موجود
            if hasattr(self, 'unified_table'):
                print(f"🔍 [DEBUG] تحميل البيانات الموحدة...")
                self.load_unified_data()
                print(f"✅ [SUCCESS] تم تحميل البيانات الموحدة بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تهيئة نافذة الواجبات الشهرية: {str(e)}")
            traceback.print_exc()
            raise

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        try:
            print(f"🔍 [DEBUG] تعيين خصائص النافذة...")
            self.setWindowTitle("إدارة الاداءات الشهرية ووجبات التسجيل")
            self.setLayoutDirection(Qt.RightToLeft)
            
            # تطبيق نمط احترافي للنافذة
            print(f"🔍 [DEBUG] تطبيق الأنماط...")
            self.setStyleSheet("""
                QMainWindow {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 1, y2: 1,
                        stop: 0 #f8f9fc,
                        stop: 1 #e9ecef
                    );
                }
                QGroupBox {
                    font-weight: bold;
                    font-size: 14px;
                    border: 2px solid #bdc3c7;
                    border-radius: 8px;
                    margin: 2px 2px;
                    padding-top: 2px;
                    background-color: white;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                    color: #2c3e50;
                }
                QTabWidget::pane {
                    border: 2px solid #bdc3c7;
                    border-radius: 8px;
                    background-color: white;
                }
                QTabWidget::tab-bar {
                    alignment: right;
                    height: 60px;
                }
                QTabBar::tab {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #ecf0f1,
                        stop: 1 #d5dbdb
                    );
                    border: 2px solid #bdc3c7;
                    border-bottom-color: #bdc3c7;
                    border-top-left-radius: 8px;
                    border-top-right-radius: 8px;
                    min-width: 200px;
                    height: 50px;
                    padding: 5px 5px;
                    margin-right: 2px;
                    font-family: 'Calibri';
                    font-size: 17px;
                    font-weight: bold;
                    color: #2c3e50;
                }
                QTabBar::tab:selected {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #e67e22,
                        stop: 1 #d35400
                    );
                    color: white;
                    border-bottom-color: white;
                    height: 55px;
                    font-size: 18px;
                }
                QTabBar::tab:hover:!selected {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #f39c12,
                        stop: 1 #e67e22
                    );
                    color: white;
                    font-size: 17px;
                }
            """)
            
            # إنشاء الواجهة المركزية
            print(f"🔍 [DEBUG] إنشاء الواجهة المركزية...")
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(5, 5, 5, 5)
            main_layout.setSpacing(10)
            
            # العنوان الرئيسي مع زر الإغلاق
            print(f"🔍 [DEBUG] إضافة العنوان الرئيسي...")
            header_frame = QFrame()
            header_frame.setMaximumHeight(50)  # تقليل الارتفاع لإزالة الحشو
            header_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 1, y2: 0,
                        stop: 0 #e67e22,
                        stop: 0.5 #d35400,
                        stop: 1 #e67e22
                    );
                    border-radius: 8px;
                    margin: 2px;
                    padding: 2px;
                }
            """)
            header_layout = QHBoxLayout(header_frame)
            header_layout.setContentsMargins(10, 5, 10, 5)  # تقليل الحشو

            title_label = QLabel("إدارة الاداءات الشهرية ووجبات التسجيل")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            title_label.setStyleSheet("color: white; font-weight: bold;")
            title_label.setAlignment(Qt.AlignLeft)

            # زر الإغلاق في شريط العنوان - في الوسط عمودياً
            close_btn = self.create_styled_button("❌ إغلاق", "#dc3545")
            close_btn.setFixedSize(90, 35)  # حجم أصغر
            close_btn.clicked.connect(self.close)

            header_layout.addWidget(title_label)
            header_layout.addStretch()
            header_layout.addWidget(close_btn, 0, Qt.AlignVCenter)  # محاذاة عمودية في الوسط

            main_layout.addWidget(header_frame)
            
            # إنشاء التبويبات
            print(f"🔍 [DEBUG] إنشاء التبويبات...")
            self.create_tabs(main_layout)
            
            print(f"✅ [SUCCESS] تم إعداد واجهة المستخدم بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد واجهة المستخدم: {str(e)}")
            traceback.print_exc()
            raise

    def showEvent(self, event):
        """عند إظهار النافذة - تعظيمها تلقائياً"""
        super().showEvent(event)
        self.showMaximized()

    def create_tabs(self, main_layout):
        """إنشاء التبويبات الرئيسية"""
        try:
            print(f"🔍 [DEBUG] إنشاء التبويبات الرئيسية...")
            
            # إنشاء عنصر التبويبات
            self.tab_widget = QTabWidget()
            
            # تطبيق خط التبويبات
            tab_font = QFont("Calibri", 17, QFont.Bold)
            self.tab_widget.setFont(tab_font)
            
            # التبويب الأول: معلومات التلميذ
            print(f"🔍 [DEBUG] إنشاء تبويب معلومات التلميذ...")
            self.student_info_tab = QWidget()
            self.create_student_info_tab()
            self.tab_widget.addTab(self.student_info_tab, " معلومات التلميذ")
            
            # التبويب الثاني: قسم الأداءات
            print(f"🔍 [DEBUG] إنشاء تبويب قسم الأداءات...")
            self.add_duty_tab = QWidget()
            self.create_add_duty_tab()
            self.tab_widget.addTab(self.add_duty_tab, " قسم الأداءات")

            # التبويب الثالث: سجل الأداءات والتقارير
            print(f"🔍 [DEBUG] إنشاء تبويب سجل الأداءات...")
            self.duties_record_tab = QWidget()
            self.create_duties_record_tab()
            self.tab_widget.addTab(self.duties_record_tab, "  سجل الأداءات")

            # التبويب الجديد: طبع التوصيل
            print(f"🔍 [DEBUG] إنشاء تبويب طبع التوصيل...")
            self.unified_records_tab = QWidget()
            self.create_unified_records_tab()
            self.tab_widget.addTab(self.unified_records_tab, "🖨️ طبع التوصيل")
            
            # إضافة التبويبات للتخطيط الرئيسي
            main_layout.addWidget(self.tab_widget)
            

            
            print(f"✅ [SUCCESS] تم إنشاء التبويبات بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التبويبات: {str(e)}")
            traceback.print_exc()
            raise

    def create_student_info_tab(self):
        """إنشاء تبويب معلومات التلميذ"""
        try:
            print(f"🔍 [DEBUG] إنشاء محتوى تبويب معلومات التلميذ...")
            
            layout = QVBoxLayout(self.student_info_tab)
            layout.setContentsMargins(10, 10, 10, 10)
            layout.setSpacing(10)
            
            # معلومات التلميذ الأساسية
            student_group = QGroupBox("📋 البيانات الأساسية")
            student_layout = QGridLayout(student_group)
            
            # تنسيق عام للتسميات
            label_style = """
                QLabel {
                    font-weight: bold;
                    color: #34495e;
                    border: none;
                    padding: 5px;
                }
            """
            
            # تنسيق عام للقيم
            value_style = """
                QLabel {
                    color: #2c3e50;
                    background-color: #ecf0f1;
                    border: 2px solid #bdc3c7;
                    border-radius: 8px;
                    padding: 5px;
                    min-height: 25px;
                }
            """
            
            # إنشاء العناصر
            print(f"🔍 [DEBUG] إنشاء عناصر معلومات التلميذ...")
            
            # معرف التلميذ
            id_label = QLabel(" معرف التلميذ:")
            id_label.setFont(QFont("Calibri", 15, QFont.Bold))
            id_label.setStyleSheet(label_style)
            self.student_id_value = QLabel(str(self.student_id))
            self.student_id_value.setFont(QFont("Calibri", 13, QFont.Bold))
            self.student_id_value.setStyleSheet(value_style)
            
            # اسم التلميذ
            name_label = QLabel(" اسم التلميذ:")
            name_label.setFont(QFont("Calibri", 15, QFont.Bold))
            name_label.setStyleSheet(label_style)
            self.student_name_value = QLabel("")
            self.student_name_value.setFont(QFont("Calibri", 13, QFont.Bold))
            self.student_name_value.setStyleSheet(value_style)
            
            # رمز التلميذ
            code_label = QLabel(" رمز التلميذ:")
            code_label.setFont(QFont("Calibri", 15, QFont.Bold))
            code_label.setStyleSheet(label_style)
            self.student_code_value = QLabel("")
            self.student_code_value.setFont(QFont("Calibri", 13, QFont.Bold))
            self.student_code_value.setStyleSheet(value_style)
            
            # المجموعة
            group_label = QLabel(" المجموعة:")
            group_label.setFont(QFont("Calibri", 15, QFont.Bold))
            group_label.setStyleSheet(label_style)
            self.student_group_value = QLabel("")
            self.student_group_value.setFont(QFont("Calibri", 13, QFont.Bold))
            self.student_group_value.setStyleSheet(value_style)
            
            # القسم
            section_label = QLabel(" القسم:")
            section_label.setFont(QFont("Calibri", 15, QFont.Bold))
            section_label.setStyleSheet(label_style)
            self.student_section_value = QLabel("")
            self.student_section_value.setFont(QFont("Calibri", 13, QFont.Bold))
            self.student_section_value.setStyleSheet(value_style)
            
            # الواجب الشهري
            monthly_duty_label = QLabel(" الواجب الشهري:")
            monthly_duty_label.setFont(QFont("Calibri", 15, QFont.Bold))
            monthly_duty_label.setStyleSheet(label_style)
            self.monthly_duty_value = QLabel("")
            self.monthly_duty_value.setFont(QFont("Calibri", 13, QFont.Bold))
            self.monthly_duty_value.setStyleSheet(value_style + "color: #27ae60; font-weight: bold;")
            
            # واجبات التسجيل
            registration_duty_label = QLabel(" واجبات التسجيل:")
            registration_duty_label.setFont(QFont("Calibri", 15, QFont.Bold))
            registration_duty_label.setStyleSheet(label_style)
            self.registration_duty_value = QLabel("")
            self.registration_duty_value.setFont(QFont("Calibri", 13, QFont.Bold))
            self.registration_duty_value.setStyleSheet(value_style + "color: #e67e22; font-weight: bold;")
            
            # ترتيب العناصر في الشبكة
            print(f"🔍 [DEBUG] ترتيب عناصر معلومات التلميذ...")
            student_layout.addWidget(id_label, 0, 0)
            student_layout.addWidget(self.student_id_value, 0, 1)
            student_layout.addWidget(name_label, 1, 0)
            student_layout.addWidget(self.student_name_value, 1, 1)
            
            student_layout.addWidget(code_label, 2, 0)
            student_layout.addWidget(self.student_code_value, 2, 1)
            student_layout.addWidget(group_label, 3, 0)
            student_layout.addWidget(self.student_group_value, 3, 1)
            
            student_layout.addWidget(section_label, 4, 0)
            student_layout.addWidget(self.student_section_value, 4, 1)
            student_layout.addWidget(monthly_duty_label, 5, 0)
            student_layout.addWidget(self.monthly_duty_value, 5, 1)
            
            # إضافة الصف الجديد لواجبات التسجيل
            student_layout.addWidget(registration_duty_label, 6, 0)
            student_layout.addWidget(self.registration_duty_value, 6, 1)
            
            # تعيين نسب الأعمدة
            student_layout.setColumnStretch(0, 1)
            student_layout.setColumnStretch(1, 2)
            
            layout.addWidget(student_group)
            layout.addStretch()
            
            print(f"✅ [SUCCESS] تم إنشاء تبويب معلومات التلميذ بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب معلومات التلميذ: {str(e)}")
            traceback.print_exc()
            raise

    def create_add_duty_tab(self):
        """إنشاء تبويب قسم الأداءات (جدول موحد)"""
        try:
            print(f"🔍 [DEBUG] إنشاء محتوى تبويب قسم الأداءات...")

            layout = QVBoxLayout(self.add_duty_tab)
            layout.setContentsMargins(10, 10, 10, 10)
            layout.setSpacing(15)

            # عنوان التبويب
            title_label = QLabel("📋 قسم الأداءات - الواجبات الشهرية وواجبات التسجيل")
            title_label.setFont(QFont("Calibri", 18, QFont.Bold))
            title_label.setStyleSheet("color: #1565C0; margin: 10px;")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # قسم جدول الأقسام والواجبات
            table_group = QGroupBox("🎯 أقسام التلميذ والواجبات المطلوبة")
            table_group.setFont(QFont("Calibri", 15, QFont.Bold))
            table_group.setStyleSheet("QGroupBox { color: #1565C0; }")
            table_layout = QVBoxLayout(table_group)
            # إنشاء جدول إدخال الواجبات
            self.create_duties_input_table()
            table_layout.addWidget(self.duties_input_table)
            layout.addWidget(table_group)

            # قسم إعدادات الدفع
            payment_group = QGroupBox("💰 إعدادات الدفع")
            payment_group.setFont(QFont("Calibri", 15, QFont.Bold))
            payment_group.setStyleSheet("QGroupBox { color: #1565C0; }")
            payment_layout = QHBoxLayout(payment_group)

            # تنسيق الحقول
            input_style = """
                QLineEdit, QDateEdit, QComboBox, QSpinBox {
                    padding: 8px;
                    border: 2px solid #bdc3c7;
                    border-radius: 8px;
                    background-color: white;
                    min-height: 25px;
                }
                QLineEdit:focus, QDateEdit:focus, QComboBox:focus, QSpinBox:focus {
                    border: 2px solid #3498db;
                    background-color: #f8f9fc;
                }
            """

            # الشهر
            month_label = QLabel("📅 الشهر:")
            month_label.setFont(QFont("Calibri", 13, QFont.Bold))
            self.month_combo = QComboBox()
            self.month_combo.addItems([
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            ])
            self.month_combo.setFont(QFont("Calibri", 13, QFont.Bold))
            self.month_combo.setStyleSheet(input_style)

            # السنة
            year_label = QLabel("📆 السنة:")
            year_label.setFont(QFont("Calibri", 13, QFont.Bold))
            self.year_spin = QSpinBox()
            self.year_spin.setRange(2020, 2030)
            self.year_spin.setValue(datetime.now().year)
            self.year_spin.setFont(QFont("Calibri", 13, QFont.Bold))
            self.year_spin.setStyleSheet(input_style)
            
            # تاريخ الدفع
            payment_date_label = QLabel("📅 تاريخ الدفع:")
            payment_date_label.setFont(QFont("Calibri", 13, QFont.Bold))
            self.payment_date = QDateEdit()
            self.payment_date.setDate(QDate.currentDate())
            self.payment_date.setCalendarPopup(True)
            self.payment_date.setFont(QFont("Calibri", 13, QFont.Bold))
            self.payment_date.setStyleSheet(input_style)

            # ملاحظات
            notes_label = QLabel("📝 ملاحظات:")
            notes_label.setFont(QFont("Calibri", 13, QFont.Bold))
            self.notes_input = QLineEdit()
            self.notes_input.setPlaceholderText("ملاحظات إضافية (اختياري)")
            self.notes_input.setFont(QFont("Calibri", 13, QFont.Bold))
            self.notes_input.setStyleSheet(input_style)

            # ترتيب عناصر الدفع
            payment_layout.addWidget(month_label)
            payment_layout.addWidget(self.month_combo)
            payment_layout.addWidget(year_label)
            payment_layout.addWidget(self.year_spin)
            payment_layout.addWidget(payment_date_label)
            payment_layout.addWidget(self.payment_date)
            payment_layout.addWidget(notes_label)
            payment_layout.addWidget(self.notes_input)

            layout.addWidget(payment_group)

            # أزرار الحفظ والتحديد
            buttons_layout = QHBoxLayout()

            # زر تحديد الكل
            select_all_button = self.create_styled_button("☑️ تحديد الكل", "#3498db")
            select_all_button.setMinimumHeight(40)
            select_all_button.clicked.connect(self.select_all_duties)

            # زر إلغاء تحديد الكل
            unselect_all_button = self.create_styled_button("☐ إلغاء تحديد الكل", "#95a5a6")
            unselect_all_button.setMinimumHeight(40)
            unselect_all_button.clicked.connect(self.unselect_all_duties)

            # زر حفظ الواجبات المحددة
            save_all_button = self.create_styled_button("💾 حفظ الواجبات المحددة", "#27ae60")
            save_all_button.setMinimumHeight(50)
            save_all_button.clicked.connect(self.save_all_duties)

            # زر مسح الجدول
            clear_button = self.create_styled_button("🗑️ مسح الجدول", "#e74c3c")
            clear_button.setMinimumHeight(50)
            clear_button.clicked.connect(self.clear_duties_table)

            buttons_layout.addWidget(select_all_button)
            buttons_layout.addWidget(unselect_all_button)
            buttons_layout.addWidget(save_all_button)
            buttons_layout.addWidget(clear_button)

            layout.addLayout(buttons_layout)
            layout.addStretch()
            
            print(f"✅ [SUCCESS] تم إنشاء تبويب إضافة واجب بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب إضافة واجب: {str(e)}")
            traceback.print_exc()
            raise

    def create_duties_record_tab(self):
        """إنشاء تبويب سجل الواجبات والتقارير"""
        try:
            print(f"🔍 [DEBUG] إنشاء محتوى تبويب سجل الواجبات...")
            
            layout = QVBoxLayout(self.duties_record_tab)
            layout.setContentsMargins(15, 15, 15, 15)
            layout.setSpacing(10)
            
            # أزرار التحكم في الجدول
            controls_frame = QFrame()
            controls_layout = QHBoxLayout(controls_frame)
            controls_layout.setSpacing(20)
            
            # زر تحديث الجدول
            refresh_table_btn = self.create_styled_button("🔄 تحديث الجدول", "#3498db")
            refresh_table_btn.clicked.connect(self.refresh_data)
            
            # زر تعديل الواجب المحدد
            edit_btn = self.create_styled_button("✏️ تعديل الواجب المحدد", "#f39c12")
            edit_btn.clicked.connect(self.edit_selected_duty)
            
            # زر حذف الواجب المحدد
            delete_btn = self.create_styled_button("🗑️ حذف الواجب المحدد", "#e74c3c")
            delete_btn.clicked.connect(self.delete_selected_duty)
            
            # زر تقرير الواجبات
            report_btn = self.create_styled_button("📋 تقرير مفصل", "#9b59b6")
            report_btn.clicked.connect(self.generate_report)

            controls_layout.addWidget(refresh_table_btn)
            controls_layout.addWidget(edit_btn)
            controls_layout.addWidget(delete_btn)
            controls_layout.addWidget(report_btn)
            controls_layout.addStretch()
            
            layout.addWidget(controls_frame)
            
            # جدول الواجبات
            table_group = QGroupBox()
            table_layout = QVBoxLayout(table_group)
            
            # إنشاء الجدول
            self.duties_table = QTableWidget()
            self.setup_duties_table()
            table_layout.addWidget(self.duties_table)
            
            layout.addWidget(table_group)
            
            print(f"✅ [SUCCESS] تم إنشاء تبويب سجل الواجبات بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب سجل الواجبات: {str(e)}")
            traceback.print_exc()
            raise



    def setup_duties_table(self):
        """إعداد جدول الواجبات"""
        try:
            print(f"🔍 [DEBUG] إعداد جدول الواجبات...")
            columns = [
                "ID", "الشهر", "السنة", "الشهري المطلوب", "الشهري المدفوع",
                "الشهري الباقي", "التسجيل المطلوب", "التسجيل المدفوع",
                "التسجيل الباقي", "تاريخ الدفع", "حالة الدفع", "تاريخ الإضافة"
            ]
            
            self.duties_table.setColumnCount(len(columns))
            self.duties_table.setHorizontalHeaderLabels(columns)
            
            # تطبيق أنماط احترافية للجدول (بدون font-size)
            self.duties_table.setStyleSheet("""
                QTableWidget {
                    background-color: #ffffff;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    gridline-color: #e0e0e0;
                    font-family: 'Calibri';
                    selection-background-color: #e3f2fd;
                }
                
                QHeaderView::section {
                    background: #f8f9fa;
                    color: #000000;
                    font-weight: bold;
                    font-family: 'Calibri';
                    font-size: 13px;
                    padding: 8px;
                    border: 1px solid #dee2e6;
                    text-align: center;
                }
                
                QTableWidget::item {
                    padding: 8px;
                    border-bottom: 1px solid #f0f0f0;
                    font-family: 'Calibri';
                }
                
                QTableWidget::item:selected {
                    background-color: #fdf2e9;
                    color: #d35400;
                }
            """)
            
            # إعدادات الجدول
            self.duties_table.setAlternatingRowColors(True)
            self.duties_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            self.duties_table.setSelectionMode(QAbstractItemView.SingleSelection)
            self.duties_table.setSortingEnabled(True)
            
            # تطبيق الخط على رأس الجدول
            header = self.duties_table.horizontalHeader()
            header.setFont(QFont("Calibri", 13, QFont.Bold))  # خط عناوين الجدول - أسود غامق
            header.setFixedHeight(45)
            header.setSectionResizeMode(QHeaderView.ResizeToContents)

            # تعيين عرض الأعمدة للجدول الجديد مع أعمدة واجبات التسجيل (بدون عمود الملاحظات)
            self.duties_table.setColumnWidth(0, 60)   # ID
            self.duties_table.setColumnWidth(1, 80)   # الشهر
            self.duties_table.setColumnWidth(2, 60)   # السنة
            self.duties_table.setColumnWidth(3, 120)  # الواجب الشهري المطلوب
            self.duties_table.setColumnWidth(4, 120)  # الواجب الشهري المدفوع
            self.duties_table.setColumnWidth(5, 120)  # الواجب الشهري المتبقي
            self.duties_table.setColumnWidth(6, 120)  # واجبات التسجيل المطلوبة
            self.duties_table.setColumnWidth(7, 120)  # واجبات التسجيل المدفوعة
            self.duties_table.setColumnWidth(8, 120)  # واجبات التسجيل المتبقية
            self.duties_table.setColumnWidth(9, 100)  # تاريخ الدفع
            self.duties_table.setColumnWidth(10, 100) # حالة الدفع
            self.duties_table.setColumnWidth(11, 120) # تاريخ الإضافة

            self.duties_table.verticalHeader().setDefaultSectionSize(35)
            self.duties_table.verticalHeader().setVisible(False)
            
            print(f"✅ [SUCCESS] تم إعداد جدول الواجبات بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد جدول الواجبات: {str(e)}")
            traceback.print_exc()
            raise

    def create_duties_table(self):
        """إنشاء جدول الواجبات الشهرية في قاعدة البيانات"""
        try:
            print(f"🔍 [DEBUG] إنشاء جدول الواجبات الشهرية في قاعدة البيانات...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الجدول أولاً
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='monthly_duties'
            """)
            table_exists = cursor.fetchone()
            
            if not table_exists:
                print(f"🔍 [DEBUG] إنشاء جدول جديد للواجبات الشهرية...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS monthly_duties (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        student_id INTEGER NOT NULL,
                        month TEXT NOT NULL,
                        year INTEGER NOT NULL,
                        amount_required REAL NOT NULL DEFAULT 0,
                        amount_paid REAL NOT NULL DEFAULT 0,
                        amount_remaining REAL NOT NULL DEFAULT 0,
                        مطلوب_التسجيل REAL NOT NULL DEFAULT 0,
                        مدفوع_التسجيل REAL NOT NULL DEFAULT 0,
                        باقي_التسجيل REAL NOT NULL DEFAULT 0,
                        payment_date TEXT,
                        payment_status TEXT NOT NULL DEFAULT 'غير مدفوع',
                        notes TEXT,
                        اسم_الاستاذ TEXT,
                        القسم TEXT,
                        المادة TEXT,
                        النوع TEXT,
                        الرمز TEXT,
                        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id),
                        UNIQUE(student_id, month, year)
                    )
                ''')
                print(f"✅ [SUCCESS] تم إنشاء جدول monthly_duties بنجاح")
            else:
                print(f"✅ [SUCCESS] جدول monthly_duties موجود بالفعل")

                # إضافة الأعمدة الجديدة إذا لم تكن موجودة (للجداول الموجودة مسبقاً)
                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN اسم_الاستاذ TEXT")
                    print(f"✅ [SUCCESS] تم إضافة عمود اسم_الاستاذ")
                except:
                    print(f"🔍 [DEBUG] عمود اسم_الاستاذ موجود بالفعل")

                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN القسم TEXT")
                    print(f"✅ [SUCCESS] تم إضافة عمود القسم")
                except:
                    print(f"🔍 [DEBUG] عمود القسم موجود بالفعل")

                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN المادة TEXT")
                    print(f"✅ [SUCCESS] تم إضافة عمود المادة")
                except:
                    print(f"🔍 [DEBUG] عمود المادة موجود بالفعل")

                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN النوع TEXT")
                    print(f"✅ [SUCCESS] تم إضافة عمود النوع")
                except:
                    print(f"🔍 [DEBUG] عمود النوع موجود بالفعل")

                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN الرمز TEXT")
                    print(f"✅ [SUCCESS] تم إضافة عمود الرمز")
                except:
                    print(f"🔍 [DEBUG] عمود الرمز موجود بالفعل")

                # إضافة الأعمدة الجديدة لواجبات التسجيل
                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN مطلوب_التسجيل REAL NOT NULL DEFAULT 0")
                    print(f"✅ [SUCCESS] تم إضافة عمود مطلوب_التسجيل")
                except:
                    print(f"🔍 [DEBUG] عمود مطلوب_التسجيل موجود بالفعل")

                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN مدفوع_التسجيل REAL NOT NULL DEFAULT 0")
                    print(f"✅ [SUCCESS] تم إضافة عمود مدفوع_التسجيل")
                except:
                    print(f"🔍 [DEBUG] عمود مدفوع_التسجيل موجود بالفعل")

                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN باقي_التسجيل REAL NOT NULL DEFAULT 0")
                    print(f"✅ [SUCCESS] تم إضافة عمود باقي_التسجيل")
                except:
                    print(f"🔍 [DEBUG] عمود باقي_التسجيل موجود بالفعل")

            conn.commit()
            conn.close()
            print(f"✅ [SUCCESS] تم إنشاء جدول الواجبات الشهرية بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] فشل في إنشاء جدول الواجبات الشهرية: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء جدول الواجبات الشهرية: {str(e)}")

    def load_student_data(self):
        """تحميل جميع بيانات التلميذ (جميع الأقسام) حسب تاريخ_الانشاء"""
        try:
            print(f"🔍 [DEBUG] تحميل جميع بيانات التلميذ ID: {self.student_id}")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # أولاً: جلب السجل الأساسي بجميع الأعمدة بالترتيب الصحيح
            cursor.execute("SELECT * FROM جدول_البيانات WHERE id = ?", (self.student_id,))
            main_record = cursor.fetchone()

            if not main_record:
                print("❌ [ERROR] لم يتم العثور على بيانات التلميذ")
                conn.close()
                QMessageBox.warning(self, "تحذير", "لا يمكن العثور على بيانات التلميذ.")
                return

            # البحث عن عمود تاريخ_الانشاء
            cursor.execute("PRAGMA table_info(جدول_البيانات)")
            columns = cursor.fetchall()
            creation_date_index = None

            for i, col in enumerate(columns):
                if col[1] == 'تاريخ_الانشاء':
                    creation_date_index = i
                    break

            if creation_date_index is not None and len(main_record) > creation_date_index:
                creation_date = main_record[creation_date_index]
                print(f"🔍 [DEBUG] تاريخ الإنشاء: {creation_date}")

                # جلب جميع السجلات بنفس تاريخ الإنشاء
                cursor.execute("""
                    SELECT * FROM جدول_البيانات
                    WHERE تاريخ_الانشاء = ?
                    ORDER BY id
                """, (creation_date,))
                all_student_records = cursor.fetchall()
                print(f"🔍 [DEBUG] تم العثور على {len(all_student_records)} سجل للتلميذ")
            else:
                # إذا لم يوجد تاريخ إنشاء، استخدم السجل الواحد فقط
                all_student_records = [main_record]
                print(f"🔍 [DEBUG] لا يوجد عمود تاريخ_الانشاء، استخدام سجل واحد")

            # حفظ جميع السجلات
            self.student_records = all_student_records
            self.student_data = main_record  # السجل الأساسي للمعلومات العامة

            print("✅ [SUCCESS] تم العثور على بيانات التلميذ")

            # تحديث معلومات التلميذ في التبويب (من السجل الأساسي)
            # الفهارس الصحيحة حسب بنية الجدول الحقيقية:
            # 1=اسم_التلميذ, 2=رمز_التلميذ, 7=اسم_المجموعة, 8=القسم
            self.student_name_value.setText(self.student_data[1] or "غير محدد")      # اسم_التلميذ
            self.student_code_value.setText(self.student_data[2] or "غير محدد")      # رمز_التلميذ
            self.student_group_value.setText(self.student_data[7] or "غير محدد")     # اسم_المجموعة
            self.student_section_value.setText(self.student_data[8] or "غير محدد")   # القسم

            # حساب إجمالي الواجبات من جميع الأقسام
            total_monthly_duty = 0.0
            total_registration_duty = 0.0

            for record in all_student_records:
                try:
                    # الواجب الشهري (العمود 11 - الواجب_الشهري)
                    monthly_duty = float(record[11]) if len(record) > 11 and record[11] else 0.0
                    # واجبات التسجيل (العمود 10 - مبلغ_التسجيل)
                    registration_duty = float(record[10]) if len(record) > 10 and record[10] else 0.0
                    total_monthly_duty += monthly_duty
                    total_registration_duty += registration_duty
                    print(f"🔍 [DEBUG] سجل {record[0]}: واجب شهري={monthly_duty}, واجب تسجيل={registration_duty}")
                except (ValueError, TypeError, IndexError):
                    continue

            self.monthly_duty_value.setText(f"{total_monthly_duty:.2f} درهم")
            self.registration_duty_value.setText(f"{total_registration_duty:.2f} درهم")

            # تحديث جدول إضافة الواجبات
            self.update_duties_table()

            conn.close()
            print("✅ [SUCCESS] تم تحميل جميع بيانات التلميذ بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل بيانات التلميذ: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التلميذ: {str(e)}")

    def update_duties_table(self):
        """تحديث جدول إضافة الواجبات بجميع أقسام التلميذ"""
        try:
            print(f"🔍 [DEBUG] تحديث جدول إضافة الواجبات...")

            if not hasattr(self, 'student_records') or not self.student_records:
                print(f"⚠️ [WARNING] لا توجد سجلات للتلميذ")
                return

            # إنشاء جدول إضافة الواجبات إذا لم يكن موجوداً
            if not hasattr(self, 'duties_input_table'):
                self.create_duties_input_table()

            # تعيين عدد الصفوف حسب عدد أقسام التلميذ
            self.duties_input_table.setRowCount(len(self.student_records))

            # ملء الجدول ببيانات الأقسام
            for row, record in enumerate(self.student_records):
                try:
                    print(f"🔍 [DEBUG] معالجة السجل {row}:")
                    print(f"🔍 [DEBUG] طول السجل: {len(record)}")

                    # طباعة كل عمود مع فهرسه وتحليل محتواه
                    for i, value in enumerate(record):
                        if value and str(value).strip():
                            value_str = str(value).strip()
                            analysis = ""
                            if "قسم" in value_str.lower():
                                analysis = " [قسم]"
                            elif value_str.replace(".", "").replace(",", "").isdigit():
                                analysis = " [رقم/مبلغ]"
                            elif "الإعداد" in value_str or "التهيئة" in value_str or "الامتحان" in value_str:
                                analysis = " [مادة دراسية]"
                            elif len(value_str) > 15 and any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in value_str):
                                analysis = " [نص طويل - محتمل مجموعة]"
                            elif len(value_str) < 15 and any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in value_str) and len(value_str.split()) <= 3:
                                analysis = " [اسم قصير - محتمل أستاذ]"
                            print(f"🔍 [DEBUG] العمود {i}: {value_str}{analysis}")
                        else:
                            print(f"🔍 [DEBUG] العمود {i}: [فارغ]")

                    # الفهارس الصحيحة حسب بنية الجدول الحقيقية:
                    # 16=المادة_الدراسية, 8=القسم, 15=اسم_الاستاذ, 7=اسم_المجموعة, 11=الواجب_الشهري

                    # إضافة مربع الاختيار المحسن في العمود الأول (مثل sub252_window.py)
                    checkbox_widget = self.create_enhanced_checkbox(row)
                    self.duties_input_table.setCellWidget(row, 0, checkbox_widget)

                    # المادة: من العمود 16 (المادة_الدراسية)
                    subject = record[16] if len(record) > 16 and record[16] else "غير محدد"
                    print(f"🔍 [DEBUG] المادة من العمود 16: {subject}")

                    subject_item = QTableWidgetItem(subject)
                    subject_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    subject_item.setFlags(subject_item.flags() & ~Qt.ItemIsEditable)
                    self.duties_input_table.setItem(row, 1, subject_item)  # العمود 1 بدلاً من 0

                    # القسم: من العمود 8 (القسم)
                    section = record[8] if len(record) > 8 and record[8] else "غير محدد"
                    print(f"🔍 [DEBUG] القسم من العمود 8: {section}")

                    section_item = QTableWidgetItem(section)
                    section_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    section_item.setFlags(section_item.flags() & ~Qt.ItemIsEditable)
                    self.duties_input_table.setItem(row, 2, section_item)  # العمود 2 بدلاً من 1

                    # الأستاذ: من العمود 15 (اسم_الاستاذ)
                    teacher = record[15] if len(record) > 15 and record[15] else "غير محدد"
                    print(f"🔍 [DEBUG] الأستاذ من العمود 15: {teacher}")

                    # إذا لم نجد الأستاذ في السجل، نبحث في جدول الأساتذة
                    if teacher == "غير محدد":
                        teacher = self.get_teacher_for_section(section)

                    teacher_item = QTableWidgetItem(teacher)
                    teacher_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    teacher_item.setFlags(teacher_item.flags() & ~Qt.ItemIsEditable)
                    self.duties_input_table.setItem(row, 3, teacher_item)  # العمود 3 بدلاً من 2

                    # المجموعة: من العمود 7 (اسم_المجموعة)
                    group = record[7] if len(record) > 7 and record[7] else "غير محدد"
                    print(f"🔍 [DEBUG] المجموعة من العمود 7: {group}")

                    group_item = QTableWidgetItem(group)
                    group_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    group_item.setFlags(group_item.flags() & ~Qt.ItemIsEditable)
                    self.duties_input_table.setItem(row, 4, group_item)  # العمود 4 بدلاً من 3

                    # الواجب الشهري: من العمود 11 (الواجب_الشهري)
                    monthly_duty = 0.0
                    if len(record) > 11 and record[11]:
                        try:
                            monthly_duty = float(record[11])
                            print(f"🔍 [DEBUG] الواجب الشهري من العمود 11: {monthly_duty}")
                        except (ValueError, TypeError):
                            monthly_duty = 0.0

                    monthly_duty_item = QTableWidgetItem(f"{monthly_duty:.2f}")
                    monthly_duty_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    monthly_duty_item.setTextAlignment(Qt.AlignCenter)
                    monthly_duty_item.setBackground(QColor("#e8f4fd"))
                    # جعل الحقل قابل للتعديل
                    monthly_duty_item.setFlags(monthly_duty_item.flags() | Qt.ItemIsEditable)
                    self.duties_input_table.setItem(row, 5, monthly_duty_item)  # العمود 5

                    # واجبات التسجيل: من العمود 10 (مبلغ_التسجيل)
                    registration_duty = 0.0
                    if len(record) > 10 and record[10]:
                        try:
                            registration_duty = float(record[10])
                            print(f"🔍 [DEBUG] واجبات التسجيل من العمود 10: {registration_duty}")
                        except (ValueError, TypeError):
                            registration_duty = 0.0

                    registration_duty_item = QTableWidgetItem(f"{registration_duty:.2f}")
                    registration_duty_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    registration_duty_item.setTextAlignment(Qt.AlignCenter)
                    registration_duty_item.setBackground(QColor("#e8f5e8"))
                    # جعل الحقل قابل للتعديل
                    registration_duty_item.setFlags(registration_duty_item.flags() | Qt.ItemIsEditable)
                    self.duties_input_table.setItem(row, 6, registration_duty_item)  # العمود 6

                    # دفع الواجب الشهري (قابل للتعديل)
                    monthly_paid_item = QTableWidgetItem("0.00")
                    monthly_paid_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    monthly_paid_item.setTextAlignment(Qt.AlignCenter)
                    monthly_paid_item.setBackground(QColor("#fff3cd"))
                    self.duties_input_table.setItem(row, 7, monthly_paid_item)  # العمود 7

                    # دفع واجبات التسجيل (قابل للتعديل أو مؤمن حسب المبلغ المطلوب)
                    registration_paid_item = QTableWidgetItem("0.00")
                    registration_paid_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    registration_paid_item.setTextAlignment(Qt.AlignCenter)

                    # إذا كانت واجبات التسجيل = 0، قم بتأمين الخلية
                    if registration_duty == 0:
                        registration_paid_item.setBackground(QColor("#f5f5f5"))  # رمادي فاتح
                        registration_paid_item.setForeground(QColor("#999999"))  # نص رمادي
                        registration_paid_item.setFlags(registration_paid_item.flags() & ~Qt.ItemIsEditable)  # تأمين الخلية
                        registration_paid_item.setToolTip("لا توجد واجبات تسجيل مطلوبة لهذا القسم")
                    else:
                        registration_paid_item.setBackground(QColor("#d5f4e6"))
                        registration_paid_item.setFlags(registration_paid_item.flags() | Qt.ItemIsEditable)  # قابل للتعديل

                    self.duties_input_table.setItem(row, 8, registration_paid_item)  # العمود 8

                    # حفظ ID السجل في البيانات المخفية
                    subject_item.setData(Qt.UserRole, record[0])  # ID السجل

                    print(f"✅ [SUCCESS] تم إضافة السجل {row}: {subject} - {section} - {group} - {monthly_duty}")

                except (IndexError, ValueError, TypeError) as e:
                    print(f"⚠️ [WARNING] خطأ في معالجة السجل {row}: {str(e)}")
                    print(f"🔍 [DEBUG] طول السجل: {len(record)}")
                    continue

            print(f"✅ [SUCCESS] تم تحديث جدول إضافة الواجبات: {len(self.student_records)} قسم")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديث جدول إضافة الواجبات: {str(e)}")
            traceback.print_exc()

    def get_teacher_for_section(self, section_name):
        """الحصول على اسم الأستاذ للقسم المحدد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # البحث في جدول الأساتذة
            cursor.execute("""
                SELECT اسم_الاستاذ FROM جدول_المواد_والاقسام
                WHERE القسم = ?
                LIMIT 1
            """, (section_name,))

            result = cursor.fetchone()
            conn.close()

            return result[0] if result and result[0] else "غير محدد"

        except Exception as e:
            print(f"❌ [ERROR] خطأ في الحصول على معلومات الأستاذ: {str(e)}")
            return "غير محدد"

    def create_duties_input_table(self):
        """إنشاء جدول إدخال الواجبات الشهرية"""
        try:
            print(f"🔍 [DEBUG] إنشاء جدول إدخال الواجبات...")

            # إنشاء الجدول
            self.duties_input_table = QTableWidget()
            self.duties_input_table.setColumnCount(9)  # زيادة عدد الأعمدة لإضافة واجبات التسجيل وعمودي الدفع

            # تعيين أسماء الأعمدة
            headers = ["اختيار", "المادة", "القسم", "الأستاذ", "المجموعة", "الواجب الشهري", "واجبات التسجيل", "دفع شهري", "دفع تسجيل"]
            self.duties_input_table.setHorizontalHeaderLabels(headers)

            # تطبيق نمط رأس الجدول فقط (بدون نمط الصفوف)
            self.duties_input_table.setStyleSheet("""
                QTableWidget {
                    gridline-color: #bdc3c7;
                    background-color: white;
                    border: 1px solid #ddd;
                }
                QHeaderView::section {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #1565C0, stop: 1 #0d47a1);
                    color: white;
                    border: none;
                    border-right: 1px solid #0d47a1;
                    font-family: 'Calibri';
                    font-size: 15px;
                    font-weight: bold;
                    text-align: center;
                    min-height: 30px;
                }
                QHeaderView::section:first {
                    border-top-left-radius: 5px;
                }
                QHeaderView::section:last {
                    border-top-right-radius: 5px;
                    border-right: none;
                }
            """)

            # تعيين عرض الأعمدة
            self.duties_input_table.setColumnWidth(0, 60)   # اختيار
            self.duties_input_table.setColumnWidth(1, 140)  # المادة
            self.duties_input_table.setColumnWidth(2, 120)  # القسم
            self.duties_input_table.setColumnWidth(3, 140)  # الأستاذ
            self.duties_input_table.setColumnWidth(4, 160)  # المجموعة
            self.duties_input_table.setColumnWidth(5, 120)  # الواجب الشهري
            self.duties_input_table.setColumnWidth(6, 120)  # واجبات التسجيل
            self.duties_input_table.setColumnWidth(7, 100)  # دفع شهري
            self.duties_input_table.setColumnWidth(8, 100)  # دفع تسجيل

            # تعيين ارتفاع الصفوف
            self.duties_input_table.verticalHeader().setDefaultSectionSize(35)
            self.duties_input_table.verticalHeader().setVisible(False)

            # تعيين خصائص الجدول (بدون تلوين الصفوف المتناوبة)
            self.duties_input_table.setSelectionBehavior(QAbstractItemView.SelectItems)  # تحديد خلايا فردية
            self.duties_input_table.horizontalHeader().setStretchLastSection(True)

            # ربط إشارة تغيير البيانات للتحقق من صحة المدخلات
            self.duties_input_table.itemChanged.connect(self.validate_input_amount)

            print(f"✅ [SUCCESS] تم إنشاء جدول إدخال الواجبات بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء جدول إدخال الواجبات: {str(e)}")
            traceback.print_exc()

    def validate_input_amount(self, item):
        """التحقق من صحة المبلغ المدخل"""
        try:
            if not item:
                return

            row = item.row()
            col = item.column()

            # التحقق فقط من أعمدة الدفع (العمود 7 و 8)
            if col not in [7, 8]:
                return

            # الحصول على المبلغ المدخل
            try:
                paid_amount = float(item.text())
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم صحيح.")
                item.setText("0.00")
                return

            # التحقق من المبلغ المطلوب
            if col == 7:  # دفع شهري
                required_item = self.duties_input_table.item(row, 5)  # الواجب الشهري المطلوب
                payment_type = "الواجب الشهري"
            else:  # col == 8, دفع تسجيل
                required_item = self.duties_input_table.item(row, 6)  # واجبات التسجيل المطلوبة
                payment_type = "واجبات التسجيل"

            if required_item:
                try:
                    required_amount = float(required_item.text())

                    # التحقق من أن المبلغ المدفوع لا يتجاوز المطلوب
                    if paid_amount > required_amount:
                        QMessageBox.warning(
                            self,
                            "تحذير",
                            f"المبلغ المدفوع ({paid_amount:.2f} درهم) يتجاوز المبلغ المطلوب ({required_amount:.2f} درهم) في {payment_type}.\n"
                            f"سيتم تعديل المبلغ إلى الحد الأقصى المسموح."
                        )
                        item.setText(f"{required_amount:.2f}")
                        return

                    # التحقق من أن المبلغ ليس سالباً
                    if paid_amount < 0:
                        QMessageBox.warning(self, "خطأ", "لا يمكن أن يكون المبلغ المدفوع سالباً.")
                        item.setText("0.00")
                        return

                except ValueError:
                    pass

            # تنسيق المبلغ إلى منزلتين عشريتين
            item.setText(f"{paid_amount:.2f}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في التحقق من صحة المدخلات: {str(e)}")
            traceback.print_exc()

    def save_all_duties(self):
        """حفظ الواجبات لجميع الأقسام من الجدول"""
        try:
            print(f"🔍 [DEBUG] بدء حفظ الواجبات لجميع الأقسام...")

            if not hasattr(self, 'duties_input_table'):
                QMessageBox.warning(self, "تحذير", "لا يوجد جدول واجبات للحفظ.")
                return

            # جمع البيانات من الجدول
            duties_to_save = []
            for row in range(self.duties_input_table.rowCount()):
                # التحقق من تحديد مربع الاختيار المحسن
                checkbox_widget = self.duties_input_table.cellWidget(row, 0)
                if checkbox_widget:
                    from PyQt5.QtWidgets import QCheckBox
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        # جمع بيانات القسم
                        subject_item = self.duties_input_table.item(row, 1)   # المادة
                        section_item = self.duties_input_table.item(row, 2)   # القسم
                        teacher_item = self.duties_input_table.item(row, 3)   # الأستاذ
                        group_item = self.duties_input_table.item(row, 4)     # المجموعة
                        monthly_duty_item = self.duties_input_table.item(row, 5)  # الواجب الشهري
                        registration_duty_item = self.duties_input_table.item(row, 6)  # واجبات التسجيل
                        monthly_paid_item = self.duties_input_table.item(row, 7)      # دفع شهري
                        registration_paid_item = self.duties_input_table.item(row, 8)  # دفع تسجيل

                        # الحصول على ID السجل
                        student_record_id = subject_item.data(Qt.UserRole) if subject_item else None

                        # التحقق من وجود مبلغ مدفوع للواجب الشهري
                        monthly_paid_amount = 0.0
                        if monthly_paid_item and monthly_paid_item.text().strip():
                            try:
                                monthly_paid_amount = float(monthly_paid_item.text())
                            except ValueError:
                                monthly_paid_amount = 0.0

                        # التحقق من وجود مبلغ مدفوع لواجبات التسجيل
                        registration_paid_amount = 0.0
                        if registration_paid_item and registration_paid_item.text().strip():
                            try:
                                registration_paid_amount = float(registration_paid_item.text())
                            except ValueError:
                                registration_paid_amount = 0.0

                        duty_data = {
                            'student_record_id': student_record_id,
                            'subject': subject_item.text() if subject_item else "",
                            'section': section_item.text() if section_item else "",
                            'teacher': teacher_item.text() if teacher_item else "",
                            'group': group_item.text() if group_item else "",
                            'monthly_duty_required': float(monthly_duty_item.text()) if monthly_duty_item and monthly_duty_item.text() else 0.0,
                            'registration_duty_required': float(registration_duty_item.text()) if registration_duty_item and registration_duty_item.text() else 0.0,
                            'monthly_paid': monthly_paid_amount,
                            'registration_paid': registration_paid_amount,
                        }
                        duties_to_save.append(duty_data)

            if not duties_to_save:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "لم يتم تحديد أي واجب للحفظ.\n"
                    "يرجى تحديد مربعات الاختيار للواجبات المطلوب حفظها."
                )
                return

            # حفظ الواجبات في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            saved_count = 0
            skipped_count = 0
            for duty in duties_to_save:
                # الحصول على رمز التلميذ من جدول_البيانات
                student_code = self.student_data[2] if len(self.student_data) > 2 and self.student_data[2] else ""

                # التحقق من وجود السجل مسبقاً
                cursor.execute("""
                    SELECT id FROM monthly_duties
                    WHERE student_id = ? AND month = ? AND year = ? AND القسم = ? AND المادة = ?
                """, (
                    duty['student_record_id'] or self.student_id,
                    self.month_combo.currentText(),
                    self.year_spin.value(),
                    duty['section'],
                    duty['subject']
                ))

                existing_record = cursor.fetchone()
                if existing_record:
                    print(f"⚠️ [WARNING] السجل موجود مسبقاً: {duty['subject']} - {duty['section']}")
                    skipped_count += 1
                    continue

                # حفظ الأداءات (الواجبات الشهرية وواجبات التسجيل) في سجل واحد
                if duty['monthly_paid'] > 0 or duty['registration_paid'] > 0:
                    monthly_amount_required = duty['monthly_duty_required']
                    monthly_amount_paid = duty['monthly_paid']
                    monthly_amount_remaining = monthly_amount_required - monthly_amount_paid

                    registration_amount_required = duty['registration_duty_required']
                    registration_amount_paid = duty['registration_paid']
                    registration_amount_remaining = registration_amount_required - registration_amount_paid

                    # تحديد حالة الدفع الإجمالية
                    total_required = monthly_amount_required + registration_amount_required
                    total_paid = monthly_amount_paid + registration_amount_paid
                    total_remaining = total_required - total_paid

                    if total_paid == 0:
                        status = "غير مدفوع"
                    elif total_paid >= total_required:
                        status = "مدفوع كاملاً"
                        total_remaining = 0
                    else:
                        status = "مدفوع جزئياً"

                    cursor.execute('''
                        INSERT INTO monthly_duties
                        (student_id, month, year, amount_required, amount_paid, amount_remaining,
                         مطلوب_التسجيل, مدفوع_التسجيل, باقي_التسجيل,
                         payment_date, payment_status, notes, اسم_الاستاذ, القسم, المادة, النوع, الرمز)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        duty['student_record_id'] or self.student_id,
                        self.month_combo.currentText(),
                        self.year_spin.value(),
                        monthly_amount_required,
                        monthly_amount_paid,
                        monthly_amount_remaining,
                        registration_amount_required,
                        registration_amount_paid,
                        registration_amount_remaining,
                        self.payment_date.date().toString("yyyy-MM-dd"),
                        status,
                        f"أداءات - {self.notes_input.text().strip()}",
                        duty['teacher'],
                        duty['section'],
                        duty['subject'],
                        "أداءات موحدة",  # النوع
                        student_code  # الرمز
                    ))
                    saved_count += 1

            conn.commit()
            conn.close()

            # رسالة النجاح مع تفاصيل السجلات المحفوظة والمتخطاة
            message = f"تم حفظ {saved_count} أداء بنجاح!\n"
            message += f"الشهر: {self.month_combo.currentText()} {self.year_spin.value()}\n"

            if skipped_count > 0:
                message += f"تم تخطي {skipped_count} سجل (محفوظ مسبقاً)\n"

            message += "تم توحيد الواجبات الشهرية وواجبات التسجيل في سجل واحد"

            QMessageBox.information(self, "نجح", message)

            # تحديث الجداول
            self.load_duties_data()
            self.clear_duties_table()

            print(f"✅ [SUCCESS] تم حفظ {saved_count} أداء بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في حفظ الواجبات: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الواجبات: {str(e)}")

    def clear_duties_table(self):
        """مسح جدول إدخال الواجبات"""
        try:
            if hasattr(self, 'duties_input_table'):
                from PyQt5.QtWidgets import QCheckBox
                for row in range(self.duties_input_table.rowCount()):
                    # إلغاء تحديد مربع الاختيار المحسن
                    checkbox_widget = self.duties_input_table.cellWidget(row, 0)
                    if checkbox_widget:
                        checkbox = checkbox_widget.findChild(QCheckBox)
                        if checkbox:
                            checkbox.setChecked(False)

                    # مسح المبلغ المدفوع للواجب الشهري
                    monthly_paid_item = self.duties_input_table.item(row, 7)  # العمود 7
                    if monthly_paid_item:
                        monthly_paid_item.setText("0.00")

                    # مسح المبلغ المدفوع لواجبات التسجيل
                    registration_paid_item = self.duties_input_table.item(row, 8)  # العمود 8
                    if registration_paid_item:
                        registration_paid_item.setText("0.00")
            print(f"✅ [SUCCESS] تم مسح جدول الواجبات")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في مسح جدول الواجبات: {str(e)}")

    def select_all_duties(self):
        """تحديد جميع الواجبات"""
        try:
            if hasattr(self, 'duties_input_table'):
                from PyQt5.QtWidgets import QCheckBox
                for row in range(self.duties_input_table.rowCount()):
                    checkbox_widget = self.duties_input_table.cellWidget(row, 0)
                    if checkbox_widget:
                        checkbox = checkbox_widget.findChild(QCheckBox)
                        if checkbox:
                            checkbox.setChecked(True)
            print(f"✅ [SUCCESS] تم تحديد جميع الواجبات")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديد جميع الواجبات: {str(e)}")

    def unselect_all_duties(self):
        """إلغاء تحديد جميع الواجبات"""
        try:
            if hasattr(self, 'duties_input_table'):
                from PyQt5.QtWidgets import QCheckBox
                for row in range(self.duties_input_table.rowCount()):
                    checkbox_widget = self.duties_input_table.cellWidget(row, 0)
                    if checkbox_widget:
                        checkbox = checkbox_widget.findChild(QCheckBox)
                        if checkbox:
                            checkbox.setChecked(False)
            print(f"✅ [SUCCESS] تم إلغاء تحديد جميع الواجبات")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إلغاء تحديد جميع الواجبات: {str(e)}")

    def create_enhanced_checkbox(self, row_index):
        """إنشاء مربع اختيار محسن مع تأثيرات سلسة وحجم أكبر مثل sub252_window.py"""
        from PyQt5.QtWidgets import QCheckBox, QWidget, QHBoxLayout

        # إنشاء مربع اختيار حقيقي مثل الموجود في sub252_window.py
        checkbox = QCheckBox()
        checkbox.setFont(QFont("Calibri", 13, QFont.Bold))
        checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 8px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 28px;
                height: 28px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498db;
                background-color: #e3f2fd;
                transform: scale(1.05);
            }
            QCheckBox::indicator:checked {
                background-color: #ffc107;
                border: 2px solid #ff8f00;
                color: #0d47a1;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #ff8f00;
                border: 2px solid #e65100;
            }
            QCheckBox::indicator:pressed {
                background-color: #e3f2fd;
                border: 2px solid #1565C0;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: #0d47a1;
                font-weight: bold;
                font-size: 18px;
                text-align: center;
            }
        """)

        # إضافة خصائص مخصصة للتتبع
        checkbox.setProperty("row_index", row_index)

        # إضافة tooltip للمساعدة
        checkbox.setToolTip("انقر للتحديد/إلغاء التحديد - مربع اختيار محسن")

        # إنشاء widget مركزي لمحاذاة مربع الاختيار في وسط الخلية
        checkbox_widget = QWidget()
        checkbox_layout = QHBoxLayout(checkbox_widget)
        checkbox_layout.addWidget(checkbox)
        checkbox_layout.setAlignment(Qt.AlignCenter)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)

        return checkbox_widget

    def create_enhanced_checkbox_unified(self, row_index):
        """إنشاء مربع اختيار محسن للجدول الموحد مثل تبويب إضافة الواجبات"""
        from PyQt5.QtWidgets import QCheckBox, QWidget, QHBoxLayout

        # إنشاء مربع اختيار حقيقي مثل الموجود في تبويب إضافة الواجبات
        checkbox = QCheckBox()
        checkbox.setFont(QFont("Calibri", 13, QFont.Bold))
        checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 8px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 28px;
                height: 28px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498db;
                background-color: #e3f2fd;
                transform: scale(1.05);
            }
            QCheckBox::indicator:checked {
                background-color: #ffc107;
                border: 2px solid #ff8f00;
                color: #0d47a1;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #ff8f00;
                border: 2px solid #e65100;
            }
            QCheckBox::indicator:pressed {
                background-color: #e3f2fd;
                border: 2px solid #1565C0;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: #0d47a1;
                font-weight: bold;
                font-size: 18px;
                text-align: center;
            }
        """)

        # ربط التغيير بدالة تحديث (تم إزالة ملخص الاختيار)
        # checkbox.stateChanged.connect(self.update_selection_summary)  # معطل

        # إضافة خصائص مخصصة للتتبع
        checkbox.setProperty("row_index", row_index)

        # إضافة tooltip للمساعدة
        checkbox.setToolTip("انقر للتحديد/إلغاء التحديد - مربع اختيار محسن")

        # إنشاء widget مركزي لمحاذاة مربع الاختيار في وسط الخلية
        checkbox_widget = QWidget()
        checkbox_layout = QHBoxLayout(checkbox_widget)
        checkbox_layout.addWidget(checkbox)
        checkbox_layout.setAlignment(Qt.AlignCenter)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)

        return checkbox_widget



    def load_duties_data(self):
        """تحميل بيانات الواجبات الشهرية"""
        try:
            print(f"🔍 [DEBUG] تحميل بيانات الواجبات الشهرية للتلميذ ID: {self.student_id}")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الجدول أولاً
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='monthly_duties'
            """)
            table_exists = cursor.fetchone()
            
            if not table_exists:
                print(f"🔍 [DEBUG] جدول monthly_duties غير موجود، سيتم إنشاؤه...")
                # إنشاء الجدول إذا لم يكن موجوداً
                self.create_duties_table()
                
                # تعيين عدد الصفوف إلى صفر
                self.duties_table.setRowCount(0)
                print(f"✅ [SUCCESS] تم إنشاء جدول فارغ")
                conn.close()
                return
            
            # الحصول على رمز التلميذ من البيانات المحملة
            student_code = self.student_data[2] if hasattr(self, 'student_data') and len(self.student_data) > 2 and self.student_data[2] else ""
            print(f"🔍 [DEBUG] تصفية الواجبات حسب رمز التلميذ: {student_code}")

            # تصفية البيانات حسب رمز التلميذ بدلاً من student_id
            if student_code:
                cursor.execute("""
                    SELECT id, month, year, amount_required, amount_paid, amount_remaining,
                           مطلوب_التسجيل, مدفوع_التسجيل, باقي_التسجيل,
                           payment_date, payment_status, created_date
                    FROM monthly_duties
                    WHERE الرمز = ?
                    ORDER BY year DESC,
                    CASE month
                        WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
                    END DESC
                """, (student_code,))
            else:
                # إذا لم يوجد رمز، استخدم student_id كما هو (للتوافق مع البيانات القديمة)
                cursor.execute("""
                    SELECT id, month, year, amount_required, amount_paid, amount_remaining,
                           مطلوب_التسجيل, مدفوع_التسجيل, باقي_التسجيل,
                           payment_date, payment_status, created_date
                    FROM monthly_duties
                    WHERE student_id = ?
                    ORDER BY year DESC,
                    CASE month
                        WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
                    END DESC
                """, (self.student_id,))
            
            records = cursor.fetchall()
            conn.close()
            
            print(f"🔍 [DEBUG] تم العثور على {len(records)} واجب شهري")
            
            # تعيين عدد الصفوف
            self.duties_table.setRowCount(len(records))
            
            # ملء الجدول بالبيانات
            for row_index, record in enumerate(records):
                for col_index, value in enumerate(record):
                    if col_index in [3, 4, 5, 6, 7, 8]:  # الأعمدة المالية (الواجبات الشهرية وواجبات التسجيل)
                        display_value = f"{float(value):.2f} درهم" if value else "0.00 درهم"
                    else:
                        display_value = str(value) if value is not None else ""

                    item = QTableWidgetItem(display_value)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 13, QFont.Bold))  # خط عناصر الجدول

                    # تلوين الأعمدة المالية
                    if col_index in [3, 4, 5]:  # أعمدة الواجبات الشهرية
                        item.setBackground(QColor("#e8f4fd"))  # أزرق فاتح
                        item.setForeground(QColor("#1565C0"))
                    elif col_index in [6, 7, 8]:  # أعمدة واجبات التسجيل
                        item.setBackground(QColor("#e8f5e8"))  # أخضر فاتح
                        item.setForeground(QColor("#2e7d32"))

                    # تلوين حسب حالة الدفع
                    if col_index == 10:  # حالة الدفع (تحديث الفهرس بعد إضافة الأعمدة الجديدة)
                        if value == "مدفوع كاملاً":
                            item.setBackground(QColor("#d5f4e6"))
                            item.setForeground(QColor("#27ae60"))
                        elif value == "مدفوع جزئياً":
                            item.setBackground(QColor("#fff3cd"))
                            item.setForeground(QColor("#856404"))
                        else:
                            item.setBackground(QColor("#f8d7da"))
                            item.setForeground(QColor("#721c24"))

                    self.duties_table.setItem(row_index, col_index, item)
            
            print(f"✅ [SUCCESS] تم تحميل بيانات الواجبات بنجاح")
                    
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل بيانات الواجبات: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الواجبات: {str(e)}")

    def add_new_duty(self):
        """إضافة واجب شهري جديد"""
        try:
            print(f"🔍 [DEBUG] بدء إضافة واجب شهري جديد...")
            
            # التحقق من البيانات المطلوبة
            if not self.amount_input.text().strip():
                print(f"⚠️ [WARNING] لم يتم إدخال المبلغ المطلوب")
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ المطلوب.")
                return
            
            amount_required = float(self.amount_input.text())
            amount_paid = float(self.paid_input.text() or 0)
            amount_remaining = amount_required - amount_paid
            
            print(f"🔍 [DEBUG] المبلغ المطلوب: {amount_required}")
            print(f"🔍 [DEBUG] المبلغ المدفوع: {amount_paid}")
            print(f"🔍 [DEBUG] المبلغ المتبقي: {amount_remaining}")
            
            # تحديد حالة الدفع
            if amount_paid == 0:
                status = "غير مدفوع"
            elif amount_paid >= amount_required:
                status = "مدفوع كاملاً"
                amount_remaining = 0
            else:
                status = "مدفوع جزئياً"
            
            print(f"🔍 [DEBUG] حالة الدفع: {status}")
            
            # التحقق من وجود واجب للشهر إذا لم يكن مسموح بدفعات متعددة
            if not self.allow_multiple_checkbox.isChecked():
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id FROM monthly_duties 
                    WHERE student_id = ? AND month = ? AND year = ?
                """, (self.student_id, self.month_combo.currentText(), self.year_spin.value()))
                
                existing_duty = cursor.fetchone()
                conn.close()
                
                if existing_duty:
                    reply = QMessageBox.question(
                        self, 
                        "واجب موجود",
                        f"يوجد واجب مسجل لشهر {self.month_combo.currentText()} {self.year_spin.value()}.\n"
                        "هل تريد إضافة دفعة إضافية لنفس الشهر؟\n"
                        "أم تفضل تعديل الواجب الموجود؟",
                        QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                        QMessageBox.Cancel
                    )
                    
                    if reply == QMessageBox.No:
                        # تحميل الواجب الموجود للتعديل
                        self.load_duty_for_edit(existing_duty[0])
                        return
                    elif reply == QMessageBox.Cancel:
                        return
                    # إذا كان Yes، سيتم المتابعة لإضافة دفعة جديدة
            
            # إدراج الواجب في قاعدة البيانات مع معلومات الأستاذ والقسم
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب معلومات الأستاذ والقسم من جدول_المواد_والاقسام
            cursor.execute("""
                SELECT
                    م.اسم_الاستاذ,
                    م.القسم
                FROM جدول_البيانات ب
                LEFT JOIN جدول_المواد_والاقسام م ON ب.القسم = م.القسم
                WHERE ب.id = ?
            """, (self.student_id,))

            teacher_info = cursor.fetchone()
            teacher_name = teacher_info[0] if teacher_info and teacher_info[0] else "غير محدد"
            section_name = teacher_info[1] if teacher_info and teacher_info[1] else "غير محدد"

            print(f"🔍 [DEBUG] معلومات الأستاذ والقسم: {teacher_name}, {section_name}")

            # جلب المادة والنوع من قاعدة البيانات
            cursor.execute("""
                SELECT
                    COALESCE(mat.المادة, '') as المادة,
                    COALESCE(jb.النوع, '') as النوع
                FROM جدول_البيانات jb
                LEFT JOIN جدول_المواد_والاقسام mat ON jb.القسم = mat.القسم
                WHERE jb.id = ?
                LIMIT 1
            """, (self.student_id,))

            result = cursor.fetchone()
            subject_name = result[0] if result and result[0] else ""
            student_type = result[1] if result and result[1] else ""

            cursor.execute('''
                INSERT INTO monthly_duties
                (student_id, month, year, amount_required, amount_paid, amount_remaining,
                 payment_date, payment_status, notes, اسم_الاستاذ, القسم, المادة, النوع)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.student_id,
                self.month_combo.currentText(),
                self.year_spin.value(),
                amount_required,
                amount_paid,
                amount_remaining,
                self.payment_date.date().toString("yyyy-MM-dd") if amount_paid > 0 else None,
                status,
                self.notes_input.text().strip(),
                teacher_name,
                section_name,
                subject_name,
                student_type
            ))
            
            conn.commit()
            conn.close()
            
            print(f"✅ [SUCCESS] تم إضافة الواجب الشهري بنجاح")
            QMessageBox.information(self, "نجح", "تم إضافة الواجب الشهري بنجاح.")
            
            # مسح الحقول وتحديث الجدول
            self.clear_form()
            self.load_duties_data()
            
        except ValueError as ve:
            print(f"❌ [VALUE ERROR] قيم غير صحيحة: {str(ve)}")
            QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم صحيحة للمبالغ.")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إضافة الواجب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الواجب: {str(e)}")

    def edit_selected_duty(self):
        """تعديل الواجب المحدد"""
        try:
            print(f"🔍 [DEBUG] محاولة تعديل الواجب المحدد...")
            current_row = self.duties_table.currentRow()
            if current_row < 0:
                print(f"⚠️ [WARNING] لم يتم تحديد واجب للتعديل")
                QMessageBox.warning(self, "تحذير", "يرجى تحديد واجب للتعديل.")
                return
            
            # الحصول على ID الواجب
            duty_id_item = self.duties_table.item(current_row, 0)
            if not duty_id_item:
                print(f"❌ [ERROR] لا يمكن تحديد ID الواجب")
                QMessageBox.warning(self, "تحذير", "لا يمكن تحديد الواجب للتعديل.")
                return
            
            duty_id = int(duty_id_item.text())
            print(f"🔍 [DEBUG] ID الواجب المحدد للتعديل: {duty_id}")
            
            # تحميل بيانات الواجب للتعديل
            self.load_duty_for_edit(duty_id)
            
            # الانتقال إلى تبويب الإضافة/التعديل
            self.tab_widget.setCurrentIndex(1)
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تعديل الواجب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل الواجب: {str(e)}")

    def load_duty_for_edit(self, duty_id):
        """تحميل بيانات الواجب للتعديل"""
        try:
            print(f"🔍 [DEBUG] تحميل بيانات الواجب للتعديل: {duty_id}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT month, year, amount_required, amount_paid, 
                       payment_date, payment_status, notes
                FROM monthly_duties 
                WHERE id = ?
            """, (duty_id,))
            
            duty_data = cursor.fetchone()
            conn.close()
            
            if duty_data:
                print(f"✅ [SUCCESS] تم العثور على بيانات الواجب")
                
                # تعبئة الحقول
                month_index = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                              "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"].index(duty_data[0])
                self.month_combo.setCurrentIndex(month_index)
                
                self.year_spin.setValue(duty_data[1])
                self.amount_input.setText(str(duty_data[2]))
                self.paid_input.setText(str(duty_data[3]))
                
                if duty_data[4]:  # تاريخ الدفع
                    payment_date = QDate.fromString(duty_data[4], "yyyy-MM-dd")
                    self.payment_date.setDate(payment_date)
                
                # تحديد حالة الدفع
                status_index = 0 if duty_data[5] == "مدفوع كاملاً" else 1
                self.status_combo.setCurrentIndex(status_index)
                
                self.notes_input.setText(duty_data[6] or "")
                
                # تعيين وضع التعديل
                self.editing_duty_id = duty_id
                self.add_button.setVisible(False)
                self.update_button.setVisible(True)
                self.cancel_edit_button.setVisible(True)
                
                # تغيير عنوان المجموعة
                add_group = self.add_duty_tab.findChild(QGroupBox)
                if add_group:
                    add_group.setTitle("✏️ تعديل الواجب الشهري")
                
                print(f"✅ [SUCCESS] تم تحميل بيانات الواجب للتعديل بنجاح")
                
            else:
                print(f"❌ [ERROR] لم يتم العثور على بيانات الواجب")
                QMessageBox.warning(self, "تحذير", "لا يمكن العثور على بيانات الواجب.")
                
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل بيانات الواجب للتعديل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الواجب: {str(e)}")

    def update_duty(self):
        """تحديث الواجب المحدد"""
        try:
            print(f"🔍 [DEBUG] بدء تحديث الواجب ID: {self.editing_duty_id}")
            
            if not self.editing_duty_id:
                print(f"❌ [ERROR] لا يوجد واجب محدد للتحديث")
                QMessageBox.warning(self, "تحذير", "لا يوجد واجب محدد للتحديث.")
                return
            
            # التحقق من البيانات المطلوبة
            if not self.amount_input.text().strip():
                print(f"⚠️ [WARNING] لم يتم إدخال المبلغ المطلوب")
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ المطلوب.")
                return
            
            amount_required = float(self.amount_input.text())
            amount_paid = float(self.paid_input.text() or 0)
            amount_remaining = amount_required - amount_paid
            
            # تحديد حالة الدفع
            if amount_paid == 0:
                status = "غير مدفوع"
            elif amount_paid >= amount_required:
                status = "مدفوع كاملاً"
                amount_remaining = 0
            else:
                status = "مدفوع جزئياً"
            
            print(f"🔍 [DEBUG] بيانات التحديث:")
            print(f"    - المبلغ المطلوب: {amount_required}")
            print(f"    - المبلغ المدفوع: {amount_paid}")
            print(f"    - المبلغ المتبقي: {amount_remaining}")
            print(f"    - حالة الدفع: {status}")
            
            # تحديث الواجب في قاعدة البيانات مع معلومات الأستاذ والقسم
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب معلومات الأستاذ والقسم المحدثة من جدول_المواد_والاقسام
            cursor.execute("""
                SELECT
                    م.اسم_الاستاذ,
                    م.القسم
                FROM جدول_البيانات ب
                LEFT JOIN جدول_المواد_والاقسام م ON ب.القسم = م.القسم
                WHERE ب.id = ?
            """, (self.student_id,))

            teacher_info = cursor.fetchone()
            teacher_name = teacher_info[0] if teacher_info and teacher_info[0] else "غير محدد"
            section_name = teacher_info[1] if teacher_info and teacher_info[1] else "غير محدد"

            print(f"🔍 [DEBUG] تحديث معلومات الأستاذ والقسم: {teacher_name}, {section_name}")

            # جلب المادة والنوع من قاعدة البيانات
            cursor.execute("""
                SELECT
                    COALESCE(mat.المادة, '') as المادة,
                    COALESCE(jb.النوع, '') as النوع
                FROM جدول_البيانات jb
                LEFT JOIN جدول_المواد_والاقسام mat ON jb.القسم = mat.القسم
                WHERE jb.id = ?
                LIMIT 1
            """, (self.student_id,))

            result = cursor.fetchone()
            subject_name = result[0] if result and result[0] else ""
            student_type = result[1] if result and result[1] else ""

            cursor.execute('''
                UPDATE monthly_duties
                SET month = ?, year = ?, amount_required = ?, amount_paid = ?,
                    amount_remaining = ?, payment_date = ?, payment_status = ?,
                    notes = ?, اسم_الاستاذ = ?, القسم = ?, المادة = ?, النوع = ?,
                    updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                self.month_combo.currentText(),
                self.year_spin.value(),
                amount_required,
                amount_paid,
                amount_remaining,
                self.payment_date.date().toString("yyyy-MM-dd") if amount_paid > 0 else None,
                status,
                self.notes_input.text().strip(),
                teacher_name,
                section_name,
                subject_name,
                student_type,
                self.editing_duty_id
            ))
            
            conn.commit()
            conn.close()
            
            print(f"✅ [SUCCESS] تم تحديث الواجب الشهري بنجاح")
            QMessageBox.information(self, "نجح", "تم تحديث الواجب الشهري بنجاح.")
            
            # إنهاء وضع التعديل وتحديث الجدول
            self.cancel_edit()
            self.load_duties_data()
            
        except ValueError as ve:
            print(f"❌ [VALUE ERROR] قيم غير صحيحة: {str(ve)}")
            QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم صحيحة للمبالغ.")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديث الواجب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث الواجب: {str(e)}")

    def cancel_edit(self):
        """إلغاء وضع التعديل"""
        try:
            print(f"🔍 [DEBUG] إلغاء وضع التعديل...")
            
            # إعادة تعيين المتغيرات
            self.editing_duty_id = None
            
            # إظهار/إخفاء الأزرار
            self.add_button.setVisible(True)
            self.update_button.setVisible(False)
            self.cancel_edit_button.setVisible(False)
            
            # إعادة تعيين عنوان المجموعة
            add_group = self.add_duty_tab.findChild(QGroupBox)
            if add_group:
                add_group.setTitle(" إضافة واجب شهري جديد")
            
            # مسح الحقول
            self.clear_form()
            
            print(f"✅ [SUCCESS] تم إلغاء وضع التعديل بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إلغاء وضع التعديل: {str(e)}")

    def clear_form(self):
        """مسح حقول النموذج"""
        try:
            print(f"🔍 [DEBUG] مسح حقول النموذج...")
            self.month_combo.setCurrentIndex(0)
            self.year_spin.setValue(datetime.now().year)
            self.paid_input.clear()
            self.payment_date.setDate(QDate.currentDate())
            self.status_combo.setCurrentIndex(0)
            self.notes_input.clear()
            self.allow_multiple_checkbox.setChecked(False)
            
            # إعادة حساب المبلغ تلقائياً
            self.calculate_registration_amount()
            
            print(f"✅ [SUCCESS] تم مسح حقول النموذج بنجاح")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في مسح حقول النموذج: {str(e)}")

    def delete_selected_duty(self):
        """حذف الواجب المحدد"""
        try:
            print(f"🔍 [DEBUG] محاولة حذف الواجب المحدد...")
            current_row = self.duties_table.currentRow()
            if current_row < 0:
                print(f"⚠️ [WARNING] لم يتم تحديد واجب للحذف")
                QMessageBox.warning(self, "تحذير", "يرجى تحديد واجب للحذف.")
                return
            
            # الحصول على ID الواجب
            duty_id_item = self.duties_table.item(current_row, 0)
            if not duty_id_item:
                print(f"❌ [ERROR] لا يمكن تحديد ID الواجب")
                QMessageBox.warning(self, "تحذير", "لا يمكن تحديد الواجب للحذف.")
                return
            
            duty_id = int(duty_id_item.text())
            print(f"🔍 [DEBUG] ID الواجب المحدد للحذف: {duty_id}")
            
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا الواجب؟\nلا يمكن التراجع عن هذا الإجراء.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                print(f"🔍 [DEBUG] تأكيد الحذف - المتابعة...")
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute("DELETE FROM monthly_duties WHERE id = ?", (duty_id,))
                
                conn.commit()
                conn.close()
                
                print(f"✅ [SUCCESS] تم حذف الواجب بنجاح")
                QMessageBox.information(self, "نجح", "تم حذف الواجب بنجاح.")
                self.load_duties_data()
            else:
                print(f"🔍 [DEBUG] تم إلغاء عملية الحذف")
                
        except Exception as e:
            print(f"❌ [ERROR] خطأ في حذف الواجب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في حذف الواجب: {str(e)}")

    def generate_report(self):
        """إنشاء تقرير الواجبات"""
        try:
            print(f"🔍 [DEBUG] إنشاء تقرير الواجبات...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الجدول أولاً
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='monthly_duties'
            """)
            table_exists = cursor.fetchone()
            
            if not table_exists:
                print(f"🔍 [DEBUG] جدول monthly_duties غير موجود")
                QMessageBox.information(self, "تقرير الواجبات", "لا توجد واجبات مسجلة للتلميذ.")
                conn.close()
                return
            
            # حساب الإحصائيات
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_duties,
                    SUM(amount_required) as total_required,
                    SUM(amount_paid) as total_paid,
                    SUM(amount_remaining) as total_remaining
                FROM monthly_duties 
                WHERE student_id = ?
            """, (self.student_id,))
            
            stats = cursor.fetchone()
            conn.close()
            
            if stats and stats[0] > 0:
                total_duties = stats[0]
                total_required = stats[1] or 0
                total_paid = stats[2] or 0
                total_remaining = stats[3] or 0
                
                payment_percentage = (total_paid / total_required * 100) if total_required > 0 else 0
                
                print(f"🔍 [DEBUG] إحصائيات التقرير:")
                print(f"    - إجمالي الواجبات: {total_duties}")
                print(f"    - إجمالي المطلوب: {total_required}")
                print(f"    - إجمالي المدفوع: {total_paid}")
                print(f"    - إجمالي المتبقي: {total_remaining}")
                print(f"    - نسبة السداد: {payment_percentage:.1f}%")
                
                report_text = f"""
📊 تقرير الواجبات الشهرية
============================

👤 اسم التلميذ: {self.student_name_value.text()}
🆔 معرف التلميذ: {self.student_id}

📈 الإحصائيات:
• إجمالي عدد الواجبات: {total_duties}
• إجمالي المبلغ المطلوب: {total_required:.2f} درهم
• إجمالي المبلغ المدفوع: {total_paid:.2f} درهم
• إجمالي المبلغ المتبقي: {total_remaining:.2f} درهم

📊 نسبة السداد: {payment_percentage:.1f}%
                """
                
                print(f"✅ [SUCCESS] تم إنشاء التقرير بنجاح")
                QMessageBox.information(self, "تقرير الواجبات", report_text)
            else:
                print(f"🔍 [DEBUG] لا توجد واجبات مسجلة")
                QMessageBox.information(self, "تقرير الواجبات", "لا توجد واجبات مسجلة للتلميذ.")
                
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التقرير: {str(e)}")
            traceback.print_exc()

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            print(f"🔍 [DEBUG] تحديث البيانات...")
            self.load_student_data()
            self.load_duties_data()
            print(f"✅ [SUCCESS] تم تحديث البيانات بنجاح")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديث البيانات: {str(e)}")
            traceback.print_exc()

    def create_styled_button(self, text, color):
        """إنشاء زر منسق"""
        try:
            button = QPushButton(text)
            button.setFont(QFont("Calibri", 13, QFont.Bold))
            button.setMinimumHeight(40)
            button.setMinimumWidth(150)
            
            # تحويل لون hex إلى لون مظلم
            dark_color = self.darken_color(color, 50)
            light_color = self.lighten_color(color, 40)
            
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 {color},
                        stop: 1 {dark_color}
                    );
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 5px 5px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 {light_color},
                        stop: 1 {color}
                    );
                }}
                QPushButton:pressed {{
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 {dark_color},
                        stop: 1 {self.darken_color(dark_color, 20)}
                    );
                }}
            """)
            return button
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء الزر المنسق: {str(e)}")
            # إنشاء زر بسيط في حالة الخطأ
            button = QPushButton(text)
            return button

    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def setup_registration_table(self):
        """إعداد جدول واجبات التسجيل"""
        try:
            print(f"🔍 [DEBUG] إعداد جدول واجبات التسجيل...")
            columns = [
                "اختيار", "ID", "نوع الدفعة", "المبلغ المدفوع", "تاريخ الدفع",
                "طريقة الدفع", "ملاحظات", "القسم", "تاريخ الإضافة"
            ]

            self.registration_table.setColumnCount(len(columns))
            self.registration_table.setHorizontalHeaderLabels(columns)
            
            # تطبيق نفس أنماط الجدول الرئيسي
            # تطبيق نمط رأس الجدول فقط (بدون نمط الصفوف)
            self.registration_table.setStyleSheet("""
                QTableWidget {
                    background-color: #ffffff;
                    border: 1px solid #ddd;
                    gridline-color: #e0e0e0;
                }
                QHeaderView::section {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #1565C0, stop: 1 #0d47a1);
                    color: white;
                    border: none;
                    border-right: 1px solid #0d47a1;
                    font-family: 'Calibri';
                    font-size: 15px;
                    font-weight: bold;
                    text-align: center;
                    min-height: 30px;
                }
                QHeaderView::section:first {
                    border-top-left-radius: 5px;
                }
                QHeaderView::section:last {
                    border-top-right-radius: 5px;
                    border-right: none;
                }
            """)
            
            # إعدادات الجدول (بدون تلوين الصفوف المتناوبة)
            self.registration_table.setSelectionBehavior(QAbstractItemView.SelectItems)
            self.registration_table.setSelectionMode(QAbstractItemView.SingleSelection)
            self.registration_table.setSortingEnabled(True)
            
            # تطبيق الخط على رأس الجدول
            header = self.registration_table.horizontalHeader()
            header.setFont(QFont("Calibri", 13, QFont.Bold))
            header.setFixedHeight(40)
            header.setSectionResizeMode(QHeaderView.ResizeToContents)
            
            self.registration_table.verticalHeader().setDefaultSectionSize(35)
            self.registration_table.verticalHeader().setVisible(False)
            
            print(f"✅ [SUCCESS] تم إعداد جدول واجبات التسجيل بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد جدول واجبات التسجيل: {str(e)}")
            traceback.print_exc()
            raise

    def create_registration_table(self):
        """إنشاء جدول واجبات التسجيل في قاعدة البيانات"""
        try:
            print(f"🔍 [DEBUG] إنشاء جدول واجبات التسجيل في قاعدة البيانات...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='registration_fees'
            """)
            table_exists = cursor.fetchone()
            
            if not table_exists:
                print(f"🔍 [DEBUG] إنشاء جدول جديد لواجبات التسجيل...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS registration_fees (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        student_id INTEGER NOT NULL,
                        payment_type TEXT NOT NULL,
                        amount_paid REAL NOT NULL DEFAULT 0,
                        payment_date TEXT NOT NULL,
                        payment_method TEXT DEFAULT 'نقداً',
                        notes TEXT,
                        القسم TEXT,
                        اسم_الاستاذ TEXT,
                        المادة TEXT,
                        النوع TEXT,
                        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
                    )
                ''')
                print(f"✅ [SUCCESS] تم إنشاء جدول registration_fees بنجاح")
            else:
                print(f"✅ [SUCCESS] جدول registration_fees موجود بالفعل")

                # التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
                cursor.execute("PRAGMA table_info(registration_fees)")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]

                # قائمة الأعمدة المطلوبة
                required_columns = [
                    ('القسم', 'TEXT'),
                    ('اسم_الاستاذ', 'TEXT'),
                    ('المادة', 'TEXT'),
                    ('النوع', 'TEXT')
                ]

                # إضافة الأعمدة المفقودة
                for column_name, column_type in required_columns:
                    if column_name not in column_names:
                        print(f"🔍 [DEBUG] إضافة عمود {column_name} إلى جدول registration_fees...")
                        try:
                            cursor.execute(f"ALTER TABLE registration_fees ADD COLUMN {column_name} {column_type}")
                            print(f"✅ [SUCCESS] تم إضافة عمود {column_name} بنجاح")
                        except sqlite3.OperationalError as e:
                            print(f"⚠️ [WARNING] العمود {column_name} موجود بالفعل أو خطأ في الإضافة: {e}")

                # تحديث السجلات الموجودة بالبيانات من الجداول المرجعية
                print(f"🔍 [DEBUG] تحديث السجلات الموجودة بالبيانات الجديدة...")
                cursor.execute("""
                    UPDATE registration_fees
                    SET
                        القسم = COALESCE(
                            (SELECT jb.القسم FROM جدول_البيانات jb WHERE jb.id = registration_fees.student_id),
                            registration_fees.القسم
                        ),
                        اسم_الاستاذ = (
                            SELECT mat.اسم_الاستاذ
                            FROM جدول_المواد_والاقسام mat
                            JOIN جدول_البيانات jb ON mat.القسم = jb.القسم
                            WHERE jb.id = registration_fees.student_id
                            LIMIT 1
                        ),
                        المادة = (
                            SELECT mat.المادة
                            FROM جدول_المواد_والاقسام mat
                            JOIN جدول_البيانات jb ON mat.القسم = jb.القسم
                            WHERE jb.id = registration_fees.student_id
                            LIMIT 1
                        ),
                        النوع = (
                            SELECT jb.النوع
                            FROM جدول_البيانات jb
                            WHERE jb.id = registration_fees.student_id
                        )
                    WHERE registration_fees.القسم IS NULL OR registration_fees.القسم = ''
                       OR registration_fees.اسم_الاستاذ IS NULL OR registration_fees.المادة IS NULL
                       OR registration_fees.النوع IS NULL
                """)
                updated_rows = cursor.rowcount
                if updated_rows > 0:
                    print(f"✅ [SUCCESS] تم تحديث {updated_rows} سجل بالبيانات الجديدة")

            conn.commit()
            conn.close()
            print(f"✅ [SUCCESS] تم إنشاء جدول واجبات التسجيل بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] فشل في إنشاء جدول واجبات التسجيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء جدول واجبات التسجيل: {str(e)}")

    def load_registration_data(self):
        """تحميل بيانات واجبات التسجيل"""
        try:
            print(f"🔍 [DEBUG] تحميل بيانات واجبات التسجيل للتلميذ ID: {self.student_id}")
            
            # إنشاء الجدول إذا لم يكن موجوداً
            self.create_registration_table()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على رمز التلميذ من البيانات المحملة
            student_code = self.student_data[2] if hasattr(self, 'student_data') and len(self.student_data) > 2 and self.student_data[2] else ""
            print(f"🔍 [DEBUG] تصفية واجبات التسجيل حسب رمز التلميذ: {student_code}")

            # تصفية البيانات حسب رمز التلميذ بدلاً من student_id
            if student_code:
                cursor.execute("""
                    SELECT rf.id, rf.payment_type, rf.amount_paid, rf.payment_date,
                           rf.payment_method, rf.notes,
                           COALESCE(jb.القسم, rf.القسم, '') as القسم,
                           rf.created_date
                    FROM registration_fees rf
                    LEFT JOIN جدول_البيانات jb ON rf.student_id = jb.id
                    WHERE jb.رمز_التلميذ = ?
                    ORDER BY rf.created_date DESC
                """, (student_code,))
            else:
                # للتوافق مع البيانات القديمة
                cursor.execute("""
                    SELECT rf.id, rf.payment_type, rf.amount_paid, rf.payment_date,
                           rf.payment_method, rf.notes,
                           COALESCE(jb.القسم, rf.القسم, '') as القسم,
                           rf.created_date
                    FROM registration_fees rf
                    LEFT JOIN جدول_البيانات jb ON rf.student_id = jb.id
                    WHERE rf.student_id = ?
                    ORDER BY rf.created_date DESC
                """, (self.student_id,))
            
            records = cursor.fetchall()
            conn.close()
            
            print(f"🔍 [DEBUG] تم العثور على {len(records)} دفعة تسجيل")
            
            # تعيين عدد الصفوف
            self.registration_table.setRowCount(len(records))
            
            # ملء الجدول بالبيانات
            for row_index, record in enumerate(records):
                # إضافة مربع الاختيار المحسن في العمود الأول
                checkbox_widget = self.create_enhanced_checkbox_registration(row_index)
                self.registration_table.setCellWidget(row_index, 0, checkbox_widget)

                # ملء باقي البيانات (بدءاً من العمود 1)
                for col_index, value in enumerate(record):
                    if col_index == 2:  # المبلغ المدفوع
                        display_value = f"{float(value):.2f} درهم" if value else "0.00 درهم"
                    else:
                        display_value = str(value) if value is not None else ""

                    item = QTableWidgetItem(display_value)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 13, QFont.Bold))

                    # تلوين المبالغ (تحديث أرقام الأعمدة بعد إضافة عمود الاختيار)
                    if col_index == 2:  # المبلغ المدفوع
                        item.setBackground(QColor("#d5f4e6"))
                        item.setForeground(QColor("#27ae60"))
                    elif col_index == 1:  # نوع الدفعة
                        if "كاملة" in str(value):
                            item.setBackground(QColor("#e8f5e8"))
                            item.setForeground(QColor("#2e7d32"))
                        else:
                            item.setBackground(QColor("#fff3e0"))
                            item.setForeground(QColor("#f57c00"))
                    elif col_index == 6:  # عمود القسم
                        item.setBackground(QColor("#e3f2fd"))
                        item.setForeground(QColor("#1976d2"))

                    self.registration_table.setItem(row_index, col_index + 1, item)  # +1 بسبب عمود الاختيار
            
            # إعادة حساب المبلغ المتبقي بعد تحديث الجدول
            self.calculate_registration_amount()
            
            print(f"✅ [SUCCESS] تم تحميل بيانات واجبات التسجيل بنجاح")
                    
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل بيانات واجبات التسجيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات واجبات التسجيل: {str(e)}")

    def add_registration_payment(self):
        """إضافة دفعة جديدة من واجبات التسجيل - معطلة مؤقتاً"""
        try:
            print(f"🔍 [DEBUG] دالة إضافة دفعة واجبات التسجيل معطلة مؤقتاً...")
            QMessageBox.information(self, "معلومات", "تم إزالة نموذج إضافة الدفعات.\nيمكنك عرض السجلات الموجودة فقط.")
            return

            # الكود القديم معطل
            # amount_paid = float(self.reg_amount_input.text())
            
            if amount_paid <= 0:
                QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر.")
                return
            
            # التحقق من عدم تجاوز المبلغ المتبقي
            total_registration_amount = self.student_data[10] if self.student_data[10] else 0
            
            # حساب المبلغ المدفوع سابقاً
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على رمز التلميذ
            student_code = self.student_data[2] if hasattr(self, 'student_data') and len(self.student_data) > 2 and self.student_data[2] else ""

            if student_code:
                cursor.execute("""
                    SELECT SUM(rf.amount_paid) as total_paid
                    FROM registration_fees rf
                    LEFT JOIN جدول_البيانات jb ON rf.student_id = jb.id
                    WHERE jb.رمز_التلميذ = ?
                """, (student_code,))
            else:
                cursor.execute("""
                    SELECT SUM(amount_paid) as total_paid
                    FROM registration_fees
                    WHERE student_id = ?
                """, (self.student_id,))
            
            result = cursor.fetchone()
            total_paid_before = result[0] if result[0] else 0
            
            # التحقق من عدم تجاوز المبلغ الإجمالي
            if (total_paid_before + amount_paid) > total_registration_amount:
                remaining = total_registration_amount - total_paid_before
                reply = QMessageBox.question(
                    self,
                    "تحذير - تجاوز المبلغ",
                    f"المبلغ المدخل ({amount_paid:.2f} درهم) يتجاوز المبلغ المتبقي ({remaining:.2f} درهم).\n"
                    f"هل تريد المتابعة بالمبلغ المتبقي فقط؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    amount_paid = remaining
                    self.reg_amount_input.setText(f"{amount_paid:.2f}")
                    print(f"🔍 [DEBUG] تم تعديل المبلغ إلى المبلغ المتبقي: {amount_paid}")
                else:
                    conn.close()
                    return
            
            print(f"🔍 [DEBUG] المبلغ المدفوع: {amount_paid}")
            
            # إنشاء الجدول إذا لم يكن موجوداً
            self.create_registration_table()
            
            # جلب البيانات المطلوبة من الجداول المرجعية
            cursor.execute('''
                SELECT
                    jb.القسم,
                    jb.النوع,
                    mat.اسم_الاستاذ,
                    mat.المادة
                FROM جدول_البيانات jb
                LEFT JOIN جدول_المواد_والاقسام mat ON jb.القسم = mat.القسم
                WHERE jb.id = ?
                LIMIT 1
            ''', (self.student_id,))

            result = cursor.fetchone()
            if result:
                student_section = result[0] if result[0] else ""
                student_type = result[1] if result[1] else ""
                teacher_name = result[2] if result[2] else ""
                subject_name = result[3] if result[3] else ""
            else:
                student_section = ""
                student_type = ""
                teacher_name = ""
                subject_name = ""

            print(f"🔍 [DEBUG] بيانات التلميذ:")
            print(f"    - القسم: {student_section}")
            print(f"    - النوع: {student_type}")
            print(f"    - اسم الأستاذ: {teacher_name}")
            print(f"    - المادة: {subject_name}")

            # إدراج الدفعة في قاعدة البيانات مع جميع البيانات
            cursor.execute('''
                INSERT INTO registration_fees
                (student_id, payment_type, amount_paid, payment_date,
                 payment_method, notes, القسم, اسم_الاستاذ, المادة, النوع)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.student_id,
                self.payment_type_combo.currentText(),
                amount_paid,
                self.reg_payment_date.date().toString("yyyy-MM-dd"),
                self.payment_method_combo.currentText(),
                self.reg_notes_input.text().strip(),
                student_section,
                teacher_name,
                subject_name,
                student_type
            ))
            
            conn.commit()
            conn.close()
            
            print(f"✅ [SUCCESS] تم إضافة دفعة واجبات التسجيل بنجاح")
            QMessageBox.information(self, "نجح", "تم إضافة دفعة واجبات التسجيل بنجاح.")
            
            # مسح الحقول وتحديث الجدول
            self.clear_registration_form()
            self.load_registration_data()
            
        except ValueError as ve:
            print(f"❌ [VALUE ERROR] قيم غير صحيحة: {str(ve)}")
            QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم صحيحة للمبلغ.")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إضافة دفعة التسجيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة دفعة التسجيل: {str(e)}")

    def clear_registration_form(self):
        """مسح حقول نموذج واجبات التسجيل - معطلة مؤقتاً"""
        try:
            print(f"🔍 [DEBUG] دالة مسح حقول نموذج واجبات التسجيل معطلة مؤقتاً...")
            # الكود القديم معطل
            return

            print(f"✅ [SUCCESS] تم مسح حقول نموذج واجبات التسجيل بنجاح")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في مسح حقول نموذج واجبات التسجيل: {str(e)}")

    def delete_selected_registration_payment(self):
        """حذف الدفعة المحددة من واجبات التسجيل"""
        try:
            print(f"🔍 [DEBUG] محاولة حذف دفعة التسجيل المحددة...")
            current_row = self.registration_table.currentRow()
            if current_row < 0:
                print(f"⚠️ [WARNING] لم يتم تحديد دفعة للحذف")
                QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة للحذف.")
                return
            
            # الحصول على ID الدفعة من العمود 1 (بعد عمود الاختيار)
            payment_id_item = self.registration_table.item(current_row, 1)
            if not payment_id_item:
                print(f"❌ [ERROR] لا يمكن تحديد ID الدفعة")
                QMessageBox.warning(self, "تحذير", "لا يمكن تحديد الدفعة للحذف.")
                return
            
            payment_id = int(payment_id_item.text())
            print(f"🔍 [DEBUG] ID الدفعة المحددة للحذف: {payment_id}")
            
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذه الدفعة؟\nلا يمكن التراجع عن هذا الإجراء.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                print(f"🔍 [DEBUG] تأكيد الحذف - المتابعة...")
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute("DELETE FROM registration_fees WHERE id = ?", (payment_id,))
                
                conn.commit()
                conn.close()
                
                print(f"✅ [SUCCESS] تم حذف الدفعة بنجاح")
                QMessageBox.information(self, "نجح", "تم حذف الدفعة بنجاح.")
                self.load_registration_data()
            else:
                print(f"🔍 [DEBUG] تم إلغاء عملية الحذف")
                
        except Exception as e:
            print(f"❌ [ERROR] خطأ في حذف دفعة التسجيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في حذف دفعة التسجيل: {str(e)}")

    def show_registration_summary(self):
        """عرض ملخص واجبات التسجيل"""
        try:
            print(f"🔍 [DEBUG] إنشاء ملخص واجبات التسجيل...")
            
            # إنشاء الجدول إذا لم يكن موجوداً
            self.create_registration_table()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حساب إحصائيات واجبات التسجيل
            # الحصول على رمز التلميذ
            student_code = self.student_data[2] if hasattr(self, 'student_data') and len(self.student_data) > 2 and self.student_data[2] else ""

            if student_code:
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_payments,
                        SUM(rf.amount_paid) as total_paid,
                        MIN(rf.payment_date) as first_payment,
                        MAX(rf.payment_date) as last_payment
                    FROM registration_fees rf
                    LEFT JOIN جدول_البيانات jb ON rf.student_id = jb.id
                    WHERE jb.رمز_التلميذ = ?
                """, (student_code,))
            else:
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_payments,
                        SUM(amount_paid) as total_paid,
                        MIN(payment_date) as first_payment,
                        MAX(payment_date) as last_payment
                    FROM registration_fees
                    WHERE student_id = ?
                """, (self.student_id,))
            
            stats = cursor.fetchone()
            
            # الحصول على إجمالي مبلغ التسجيل المطلوب من بيانات التلميذ
            total_required = self.student_data[10] if self.student_data and self.student_data[10] else 0
            
            conn.close()
            
            if stats and stats[0] > 0:
                total_payments = stats[0]
                total_paid = stats[1] or 0
                first_payment = stats[2] or "غير محدد"
                last_payment = stats[3] or "غير محدد"
                
                remaining_amount = max(0, total_required - total_paid)
                payment_percentage = (total_paid / total_required * 100) if total_required > 0 else 0
                
                print(f"🔍 [DEBUG] إحصائيات واجبات التسجيل:")
                print(f"    - إجمالي الدفعات: {total_payments}")
                print(f"    - إجمالي المدفوع: {total_paid}")
                print(f"    - المبلغ المتبقي: {remaining_amount}")
                print(f"    - نسبة السداد: {payment_percentage:.1f}%")
                
                summary_text = f"""
💰 ملخص واجبات التسجيل والتقسيط
=====================================

👤 اسم التلميذ: {self.student_name_value.text()}
🆔 معرف التلميذ: {self.student_id}

📊 الإحصائيات المالية:
• إجمالي مبلغ التسجيل المطلوب: {total_required:.2f} درهم
• إجمالي المبلغ المدفوع: {total_paid:.2f} درهم
• المبلغ المتبقي: {remaining_amount:.2f} درهم
• نسبة السداد: {payment_percentage:.1f}%

📈 إحصائيات الدفعات:
• عدد الدفعات المسجلة: {total_payments}
• تاريخ أول دفعة: {first_payment}
• تاريخ آخر دفعة: {last_payment}

💡 الحالة: {"مكتمل السداد ✅" if remaining_amount == 0 else f"متبقي {remaining_amount:.2f} درهم ⏳"}
                """
                
                print(f"✅ [SUCCESS] تم إنشاء ملخص واجبات التسجيل بنجاح")
                QMessageBox.information(self, "ملخص واجبات التسجيل", summary_text)
            else:
                print(f"🔍 [DEBUG] لا توجد دفعات تسجيل مسجلة")
                QMessageBox.information(
                    self, 
                    "ملخص واجبات التسجيل", 
                    f"""
💰 ملخص واجبات التسجيل والتقسيط
=====================================

👤 اسم التلميذ: {self.student_name_value.text()}
🆔 معرف التلميذ: {self.student_id}

📊 الحالة: لا توجد دفعات تسجيل مسجلة بعد
💡 إجمالي مبلغ التسجيل المطلوب: {total_required:.2f} درهم
                    """
                )
                
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء ملخص واجبات التسجيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء ملخص واجبات التسجيل: {str(e)}")

    def create_bottom_buttons(self, main_layout):
        """إنشاء الأزرار السفلية"""
        try:
            print(f"🔍 [DEBUG] إنشاء الأزرار السفلية...")
            buttons_frame = QFrame()
            buttons_frame.setStyleSheet("""
                QFrame {
                    background-color: #f8f9fa;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    padding: 10px;
                    margin: 5px 0px;
                }
            """)
            buttons_layout = QHBoxLayout(buttons_frame)
            buttons_layout.setSpacing(15)
            
            # زر العودة للتبويب الأول
            first_tab_btn = self.create_styled_button("📋 معلومات التلميذ", "#17a2b8")
            first_tab_btn.clicked.connect(lambda: self.tab_widget.setCurrentIndex(0))
            
            # زر العودة للتبويب الثاني
            second_tab_btn = self.create_styled_button("➕ قسم الأداءات", "#28a745")
            second_tab_btn.clicked.connect(lambda: self.tab_widget.setCurrentIndex(1))

            # زر العودة للتبويب الثالث
            third_tab_btn = self.create_styled_button("📊 سجل الأداءات", "#6f42c1")
            third_tab_btn.clicked.connect(lambda: self.tab_widget.setCurrentIndex(2))
            
            # زر إغلاق
            close_btn = self.create_styled_button("❌ إغلاق النافذة", "#6c757d")
            close_btn.clicked.connect(self.close)
            
            # زر العودة للتبويب الرابع (طبع التوصيل)
            fourth_tab_btn = self.create_styled_button("🖨️ طبع التوصيل", "#17a2b8")
            fourth_tab_btn.clicked.connect(lambda: self.tab_widget.setCurrentIndex(3))

            buttons_layout.addWidget(first_tab_btn)
            buttons_layout.addWidget(second_tab_btn)
            buttons_layout.addWidget(third_tab_btn)
            buttons_layout.addWidget(fourth_tab_btn)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_btn)
            
            main_layout.addWidget(buttons_frame)
            print(f"✅ [SUCCESS] تم إنشاء الأزرار السفلية بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء الأزرار السفلية: {str(e)}")
            traceback.print_exc()
            raise

    def calculate_registration_amount(self):
        """حساب مبلغ واجبات التسجيل تلقائياً"""
        try:
            print(f"🔍 [DEBUG] حساب مبلغ واجبات التسجيل...")
            
            if not self.student_data:
                return
            
            # الحصول على إجمالي مبلغ التسجيل من بيانات التلميذ
            total_registration_amount = self.student_data[10] if self.student_data[10] else 0
            
            # حساب المبلغ المدفوع سابقاً
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # الحصول على رمز التلميذ
                student_code = self.student_data[2] if hasattr(self, 'student_data') and len(self.student_data) > 2 and self.student_data[2] else ""

                if student_code:
                    cursor.execute("""
                        SELECT SUM(rf.amount_paid) as total_paid
                        FROM registration_fees rf
                        LEFT JOIN جدول_البيانات jb ON rf.student_id = jb.id
                        WHERE jb.رمز_التلميذ = ?
                    """, (student_code,))
                else:
                    cursor.execute("""
                        SELECT SUM(amount_paid) as total_paid
                        FROM registration_fees
                        WHERE student_id = ?
                    """, (self.student_id,))
                
                result = cursor.fetchone()
                total_paid = result[0] if result[0] else 0
                conn.close()
                
            except Exception as e:
                print(f"⚠️ [WARNING] خطأ في حساب المبلغ المدفوع سابقاً: {str(e)}")
                total_paid = 0
            
            # حساب المبلغ المتبقي
            remaining_amount = max(0, total_registration_amount - total_paid)
            
            # تحديد المبلغ حسب نوع الدفعة
            payment_type = self.payment_type_combo.currentText()
            
            if "كاملة" in payment_type:
                # دفعة كاملة = المبلغ المتبقي كاملاً
                suggested_amount = remaining_amount
            else:
                # دفعة مقسطة = نصف المبلغ المتبقي أو حد أدنى
                suggested_amount = max(100, remaining_amount / 2) if remaining_amount > 200 else remaining_amount
            
            # تحديث حقل المبلغ
            if not self.reg_amount_input.isReadOnly():
                # إذا كان الحقل قابل للتحرير، لا نغير قيمته
                pass
            else:
                # تعيين المبلغ المقترح
                self.reg_amount_input.setText(f"{suggested_amount:.2f}")
            
            # تحديث عرض المبلغ المتبقي
            self.remaining_amount_display.setText(f"{remaining_amount:.2f} درهم")
            
            # تغيير لون المبلغ المتبقي حسب الحالة
            if remaining_amount == 0:
                self.remaining_amount_display.setStyleSheet("""
                    QLabel {
                        color: #27ae60;
                        background-color: #d5f4e6;
                        border: 2px solid #27ae60;
                        border-radius: 8px;
                        padding: 5px;
                        min-height: 25px;
                        font-weight: bold;
                    }
                """)
            elif remaining_amount < 500:
                self.remaining_amount_display.setStyleSheet("""
                    QLabel {
                        color: #f39c12;
                        background-color: #fef9e7;
                        border: 2px solid #f39c12;
                        border-radius: 8px;
                        padding: 5px;
                        min-height: 25px;
                        font-weight: bold;
                    }
                """)
            else:
                self.remaining_amount_display.setStyleSheet("""
                    QLabel {
                        color: #e74c3c;
                        background-color: #fadbd8;
                        border: 2px solid #e74c3c;
                        border-radius: 8px;
                        padding: 5px;
                        min-height: 25px;
                        font-weight: bold;
                    }
                """)
            
            print(f"🔍 [DEBUG] تم حساب مبلغ التسجيل:")
            print(f"    - إجمالي المطلوب: {total_registration_amount:.2f}")
            print(f"    - المدفوع سابقاً: {total_paid:.2f}")
            print(f"    - المتبقي: {remaining_amount:.2f}")
            print(f"    - المبلغ المقترح: {suggested_amount:.2f}")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في حساب مبلغ واجبات التسجيل: {str(e)}")
            traceback.print_exc()

    def toggle_amount_edit(self):
        """تبديل وضع تحرير المبلغ"""
        try:
            print(f"🔍 [DEBUG] تبديل وضع تحرير المبلغ...")
            
            if self.reg_amount_input.isReadOnly():
                # تفعيل التحرير
                self.reg_amount_input.setReadOnly(False)
                self.reg_amount_input.setPlaceholderText("أدخل المبلغ المطلوب يدوياً")
                self.reg_amount_input.setStyleSheet("""
                    QLineEdit {
                        padding: 5px;
                        border: 2px solid #3498db;
                        border-radius: 8px;
                        background-color: #f8f9fc;
                        min-height: 20px;
                    }
                """)
                print(f"✅ [SUCCESS] تم تفعيل تحرير المبلغ")
            else:
                # إلغاء التحرير والعودة للحساب التلقائي
                self.reg_amount_input.setReadOnly(True)
                self.reg_amount_input.setPlaceholderText("سيتم حساب المبلغ تلقائياً")
                self.reg_amount_input.setStyleSheet("""
                    QLineEdit {
                        padding: 5px;
                        border: 2px solid #bdc3c7;
                        border-radius: 8px;
                        background-color: white;
                        min-height: 20px;
                    }
                """)
                # إعادة حساب المبلغ تلقائياً
                self.calculate_registration_amount()
                print(f"✅ [SUCCESS] تم إلغاء تحرير المبلغ والعودة للحساب التلقائي")
                
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تبديل وضع تحرير المبلغ: {str(e)}")
            traceback.print_exc()

    def print_monthly_duty_receipt(self):
        """طباعة التوصيل السريع للواجب الشهري المحدد"""
        try:
            print(f"🔍 [DEBUG] بدء طباعة التوصيل السريع للواجب الشهري...")

            # التحقق من تحديد سجل
            current_row = self.duties_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد واجب شهري لطباعة التوصيل.")
                return

            # الحصول على بيانات الواجب المحدد
            duty_id = self.duties_table.item(current_row, 0).text()
            month = self.duties_table.item(current_row, 1).text()
            year = self.duties_table.item(current_row, 2).text()

            # الواجبات الشهرية
            monthly_required = self.duties_table.item(current_row, 3).text()
            monthly_paid = self.duties_table.item(current_row, 4).text()

            # واجبات التسجيل
            registration_required = self.duties_table.item(current_row, 6).text() if self.duties_table.columnCount() > 6 else "0.00 درهم"
            registration_paid = self.duties_table.item(current_row, 7).text() if self.duties_table.columnCount() > 7 else "0.00 درهم"

            payment_date = self.duties_table.item(current_row, 9).text() if self.duties_table.columnCount() > 9 else ""
            payment_status = self.duties_table.item(current_row, 10).text() if self.duties_table.columnCount() > 10 else ""

            # إنشاء محتوى التوصيل
            receipt_content = self.create_receipt_content(
                receipt_type="واجب شهري",
                month=month,
                year=year,
                amount_required=monthly_required,
                amount_paid=monthly_paid,
                registration_required=registration_required,
                registration_paid=registration_paid,
                payment_date=payment_date,
                payment_status=payment_status,
                duty_id=duty_id
            )

            # طباعة التوصيل
            self.send_to_thermal_printer(receipt_content)

            print(f"✅ [SUCCESS] تم إرسال التوصيل للطباعة بنجاح")
            QMessageBox.information(self, "نجح", "تم إرسال التوصيل للطباعة بنجاح.")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة التوصيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التوصيل: {str(e)}")

    def print_registration_receipt(self):
        """طباعة التوصيل السريع لواجب التسجيل المحدد"""
        try:
            print(f"🔍 [DEBUG] بدء طباعة التوصيل السريع لواجب التسجيل...")

            # التحقق من تحديد سجل
            current_row = self.registration_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة تسجيل لطباعة التوصيل.")
                return

            # الحصول على بيانات الدفعة المحددة
            payment_id = self.registration_table.item(current_row, 0).text()
            payment_type = self.registration_table.item(current_row, 1).text()
            amount_paid = self.registration_table.item(current_row, 2).text()
            payment_date = self.registration_table.item(current_row, 3).text()
            payment_method = self.registration_table.item(current_row, 4).text()

            # إنشاء محتوى التوصيل
            receipt_content = self.create_receipt_content(
                receipt_type="واجب تسجيل",
                payment_type=payment_type,
                amount_paid=amount_paid,
                payment_date=payment_date,
                payment_method=payment_method,
                payment_id=payment_id
            )

            # طباعة التوصيل
            self.send_to_thermal_printer(receipt_content)

            print(f"✅ [SUCCESS] تم إرسال التوصيل للطباعة بنجاح")
            QMessageBox.information(self, "نجح", "تم إرسال التوصيل للطباعة بنجاح.")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة التوصيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التوصيل: {str(e)}")

    def create_receipt_content(self, receipt_type, **kwargs):
        """إنشاء محتوى التوصيل"""
        try:
            print(f"🔍 [DEBUG] إنشاء محتوى التوصيل لنوع: {receipt_type}")

            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT المؤسسة, رقم_الهاتف, المدينة FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()
            conn.close()

            institution_name = institution_data[0] if institution_data and institution_data[0] else "المؤسسة التعليمية"
            institution_phone = institution_data[1] if institution_data and institution_data[1] else ""
            institution_city = institution_data[2] if institution_data and institution_data[2] else ""

            # بناء محتوى الوصل - تصميم مخصص
            receipt_lines = []
            # إطار حول اسم المؤسسة والمدينة
            receipt_lines.append("=" * 40)
            receipt_lines.append(f"{institution_name}".center(40))
            if institution_city:
                receipt_lines.append(f"{institution_city}".center(40))
            receipt_lines.append("=" * 40)
            # الهاتف خارج الإطار
            if institution_phone:
                receipt_lines.append(f"هاتف: {institution_phone}".center(40))
            # وصل الأداء في الوسط
            if receipt_type == "واجب شهري":
                receipt_lines.append("وصل الأداء".center(40))
            elif receipt_type == "واجب تسجيل":
                receipt_lines.append("وصل التسجيل".center(40))

            # بيانات التلميذ - محاذاة إلى اليمين
            if self.student_data:
                receipt_lines.append(f"اسم التلميذ: {self.student_data[1] or 'غير محدد'}".rjust(40))
                receipt_lines.append(f"رمز التلميذ: {self.student_data[2] or 'غير محدد'}".rjust(40))
                # إزالة "/" من القسم
                section_name = self.student_data[8] or 'غير محدد'
                if '/' in section_name:
                    section_name = section_name.replace('/', '')
                receipt_lines.append(f"القسم: {section_name}".rjust(40))

            receipt_lines.append("-" * 40)

            # تفاصيل الدفع حسب النوع - محاذاة إلى اليمين مع إضافة "درهم"
            if receipt_type == "واجب شهري":
                receipt_lines.append(f"الشهر: {kwargs.get('month', '')}".rjust(40))

                # الواجبات الشهرية
                monthly_required = float(kwargs.get('amount_required', 0))
                monthly_paid = float(kwargs.get('amount_paid', 0))
                monthly_remaining = monthly_required - monthly_paid

                if monthly_required > 0:
                    receipt_lines.append("الواجبات الشهرية:".rjust(40))
                    receipt_lines.append(f"المطلوب: {monthly_required:.2f} درهم".rjust(40))
                    receipt_lines.append(f"المدفوع: {monthly_paid:.2f} درهم".rjust(40))
                    receipt_lines.append(f"الباقي: {monthly_remaining:.2f} درهم".rjust(40))

                # واجبات التسجيل (إذا كانت أكبر من 0)
                registration_required = float(kwargs.get('registration_required', 0))
                registration_paid = float(kwargs.get('registration_paid', 0))
                registration_remaining = registration_required - registration_paid

                if registration_required > 0:
                    receipt_lines.append("واجبات التسجيل:".rjust(40))
                    receipt_lines.append(f"المطلوب: {registration_required:.2f} درهم".rjust(40))
                    receipt_lines.append(f"المدفوع: {registration_paid:.2f} درهم".rjust(40))
                    receipt_lines.append(f"الباقي: {registration_remaining:.2f} درهم".rjust(40))

                # الإجمالي
                total_paid = monthly_paid + registration_paid
                if total_paid > 0:
                    receipt_lines.append("-" * 25)
                    receipt_lines.append(f"إجمالي المبلغ المدفوع: {total_paid:.2f} درهم".rjust(40))

                receipt_lines.append(f"تاريخ الدفع: {kwargs.get('payment_date', '')}".rjust(40))
                receipt_lines.append(f"حالة الدفع: {kwargs.get('payment_status', '')}".rjust(40))
                receipt_lines.append(f"رقم الوصل: {kwargs.get('duty_id', '')}".rjust(40))

            elif receipt_type == "واجب تسجيل":
                receipt_lines.append(f"نوع الدفعة: {kwargs.get('payment_type', '')}".rjust(40))

                # إضافة "درهم" للمبلغ
                amount_paid = kwargs.get('amount_paid', '')
                receipt_lines.append(f"المبلغ المدفوع: {amount_paid}".rjust(40))

                receipt_lines.append(f"تاريخ الدفع: {kwargs.get('payment_date', '')}".rjust(40))
                receipt_lines.append(f"طريقة الدفع: {kwargs.get('payment_method', '')}".rjust(40))
                receipt_lines.append(f"رقم الوصل: {kwargs.get('payment_id', '')}".rjust(40))

            receipt_lines.append(f"تاريخ الطباعة {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".rjust(40))
            receipt_lines.append("")  # سطر فارغ
            receipt_lines.append("نتمنى لكم التوفيق والنجاح".center(40))

            return "\n".join(receipt_lines)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء محتوى التوصيل: {str(e)}")
            traceback.print_exc()
            return "خطأ في إنشاء التوصيل"

    def send_to_thermal_printer(self, content):
        """إرسال المحتوى للطابعة الحرارية المحفوظة في قاعدة البيانات"""
        try:
            print(f"🔍 [DEBUG] إرسال المحتوى للطابعة الحرارية...")

            # الحصول على اسم الطابعة الحرارية من قاعدة البيانات
            thermal_printer = self.get_thermal_printer_name()

            if not thermal_printer:
                QMessageBox.warning(self, "تحذير", "لم يتم تعيين طابعة حرارية. يرجى الذهاب إلى إعدادات الطابعة أولاً.")
                return

            # طباعة مباشرة على الطابعة الحرارية بدون حفظ ملف
            self.print_directly_to_thermal_printer(thermal_printer, content)

            print(f"✅ [SUCCESS] تم إرسال التوصيل للطابعة الحرارية: {thermal_printer}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إرسال المحتوى للطابعة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ في الطباعة", f"فشل في الطباعة على الطابعة الحرارية:\n{str(e)}")

    def get_thermal_printer_name(self):
        """الحصول على اسم الطابعة الحرارية من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT الطابعة_الحرارية FROM إعدادات_الطابعة LIMIT 1")
            result = cursor.fetchone()

            conn.close()

            if result and result[0]:
                return result[0]
            else:
                return None

        except Exception as e:
            print(f"❌ [ERROR] خطأ في الحصول على اسم الطابعة الحرارية: {str(e)}")
            return None

    def print_directly_to_thermal_printer(self, printer_name, content):
        """طباعة مباشرة على الطابعة الحرارية بدون حفظ ملف - باستخدام جدول"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont, QFontMetrics, QPen
            from PyQt5.QtCore import QSizeF, QRect, Qt

            # إعداد الطابعة
            printer = QPrinter()
            printer.setPrinterName(printer_name)

            # إعداد حجم الورقة للطابعة الحرارية (80mm)
            printer.setPageSize(QPrinter.Custom)
            printer.setPageSizeMM(QSizeF(75, 170))  # عرض 75 ارتفاع 170

            # إعداد الهوامش - 0.4 من كل الجهات
            printer.setPageMargins(0.4, 0.4, 0.4, 0.4, QPrinter.Millimeter)

            # إعداد الرسام
            painter = QPainter()
            if painter.begin(printer):
                # إعداد الخط Calibri 12 أسود غامق
                font = QFont("Calibri", 12, QFont.Bold)
                painter.setFont(font)
                painter.setPen(Qt.black)

                # حساب مقاييس الخط
                font_metrics = QFontMetrics(font)
                line_height = font_metrics.height() + 4  # إضافة مسافة بين الأسطر

                # تقسيم المحتوى إلى أسطر
                lines = content.split('\n')

                # رسم الوصل باستخدام جدول منظم
                y_position = 20  # البدء من الأعلى
                page_width = printer.pageRect().width() - 20  # عرض الصفحة مع الهوامش

                # حساب عرض الجدول (نفس العرض المستخدم في draw_table)
                table_width = int(page_width)  # عرض الجدول
                table_x = 10  # موضع بداية الجدول

                # متغيرات لتجميع بيانات الجدول
                table_data = []
                table_start_y = None
                in_table_section = False

                # رسم العناوين (في الوسط) - رسم الخطوط المزخرفة حول المؤسسة فقط
                for i, line in enumerate(lines):
                    # رسم الخطوط المزخرفة حول المؤسسة والمدينة، تجاهل باقي الخطوط
                    if line.strip().startswith('='):
                        # رسم الخطوط المزخرفة بعرض الجدول
                        # حساب عدد الرموز المطلوبة لملء عرض الجدول
                        char_width = font_metrics.width('=')
                        num_chars = int(table_width / char_width)
                        decorative_line = '=' * num_chars

                        # رسم الخط المزخرف بنفس موضع الجدول
                        painter.drawText(table_x, int(y_position), decorative_line)
                        y_position += line_height
                        continue
                    elif line.strip().startswith('-'):
                        # تجاهل الخطوط المتقطعة
                        continue

                    if ('وصل الأداء' in line or 'وصل التسجيل' in line or
                        'شكراً لكم' in line or
                        (i < 5 and not ':' in line and not 'تاريخ الطباعة' in line)):

                        # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                        if table_data and table_start_y is not None:
                            y_position = self.draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                            table_data = []
                            table_start_y = None
                            in_table_section = False

                        # رسم العناوين في الوسط
                        text_width = font_metrics.width(line)
                        x_position = int((page_width - text_width) / 2 + 10)
                        x_position = max(10, x_position)

                        painter.drawText(x_position, int(y_position), line)
                        y_position += line_height

                    elif ':' in line and not line.strip().startswith('تاريخ الطباعة'):
                        # تجميع البيانات لرسم جدول واحد
                        if not in_table_section:
                            table_start_y = y_position
                            in_table_section = True

                        parts = line.split(':', 1)
                        if len(parts) == 2:
                            label = parts[0].strip()
                            value = parts[1].strip()
                            table_data.append((label, value))

                    elif line.strip().startswith('تاريخ الطباعة'):
                        # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                        if table_data and table_start_y is not None:
                            y_position = self.draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                            table_data = []
                            table_start_y = None
                            in_table_section = False

                        # رسم تاريخ الطباعة في الوسط
                        text_width = font_metrics.width(line)
                        x_position = int((page_width - text_width) / 2 + 10)
                        x_position = max(10, x_position)

                        painter.drawText(x_position, int(y_position), line)
                        y_position += line_height

                # رسم الجدول الأخير إذا كان متبقي
                if table_data and table_start_y is not None:
                    y_position = self.draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)

                painter.end()
                print(f"✅ [SUCCESS] تم إرسال الوصل للطباعة على: {printer_name}")
            else:
                raise Exception(f"فشل في بدء الطباعة على الطابعة: {printer_name}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في الطباعة المباشرة: {str(e)}")
            raise

    def draw_table(self, painter, table_data, start_y, page_width, line_height, font_metrics):
        """رسم جدول حقيقي بخطوط وحدود"""
        try:
            from PyQt5.QtGui import QPen
            from PyQt5.QtCore import QRect, Qt

            if not table_data:
                return start_y

            # إعدادات الجدول - عكس الأعمدة
            col1_width = int(page_width * 0.6)  # 60% للقيمة (العمود الأول الآن)
            col2_width = int(page_width * 0.4)  # 40% للتسمية (العمود الثاني الآن)
            table_x = 10
            table_width = col1_width + col2_width
            row_height = line_height + 4  # ارتفاع أكبر للصفوف

            # حساب ارتفاع الجدول الكامل
            table_height = len(table_data) * row_height

            # رسم الإطار الخارجي للجدول
            painter.setPen(QPen(Qt.black, 2))  # خط أسود سميك
            table_rect = QRect(table_x, int(start_y), table_width, table_height)
            painter.drawRect(table_rect)

            # رسم الخط الفاصل بين العمودين
            painter.setPen(QPen(Qt.black, 1))  # خط أسود عادي
            separator_x = table_x + col1_width
            painter.drawLine(separator_x, int(start_y), separator_x, int(start_y + table_height))

            # رسم الخطوط الأفقية بين الصفوف
            for i in range(1, len(table_data)):
                y_line = int(start_y + i * row_height)
                painter.drawLine(table_x, y_line, table_x + table_width, y_line)

            # رسم النصوص في الجدول
            painter.setPen(Qt.black)  # لون النص

            for i, (label, value) in enumerate(table_data):
                row_y = int(start_y + i * row_height)

                # رسم القيمة في العمود الأول (محاذاة يمين) - معكوس
                value_rect = QRect(table_x + 5, row_y + 2, col1_width - 10, row_height - 4)
                painter.drawText(value_rect, Qt.AlignRight | Qt.AlignVCenter, value)

                # رسم التسمية في العمود الثاني (محاذاة يمين) - معكوس وبدون ":"
                label_rect = QRect(separator_x + 5, row_y + 2, col2_width - 10, row_height - 4)
                painter.drawText(label_rect, Qt.AlignRight | Qt.AlignVCenter, label)

            # إرجاع الموضع Y الجديد بعد الجدول
            return start_y + table_height + line_height

        except Exception as e:
            print(f"❌ [ERROR] خطأ في رسم الجدول: {str(e)}")
            return start_y

    def create_unified_records_tab(self):
        """إنشاء التبويب الموحد للسجلات مع مربعات الاختيار"""
        try:
            print(f"🔍 [DEBUG] إنشاء التبويب الموحد...")

            layout = QVBoxLayout(self.unified_records_tab)
            layout.setSpacing(15)
            layout.setContentsMargins(20, 20, 20, 20)

            # عنوان التبويب
            title_label = QLabel("🖨️ طبع التوصيل - الأداءات الموحدة")
            title_label.setFont(QFont("Calibri", 18, QFont.Bold))
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 1, y2: 0,
                        stop: 0 #3498db,
                        stop: 1 #2980b9
                    );
                    color: white;
                    padding: 15px;
                    border-radius: 10px;
                    margin-bottom: 10px;
                }
            """)
            layout.addWidget(title_label)

            # أزرار التحكم
            controls_frame = QFrame()
            controls_layout = QHBoxLayout(controls_frame)

            # زر تحديث البيانات
            refresh_btn = self.create_styled_button("🔄 تحديث البيانات", "#27ae60")
            refresh_btn.clicked.connect(self.load_unified_data)

            # زر تحديد الكل
            select_all_btn = self.create_styled_button("☑️ تحديد الكل", "#3498db")
            select_all_btn.clicked.connect(self.select_all_records)

            # زر إلغاء تحديد الكل
            deselect_all_btn = self.create_styled_button("☐ إلغاء تحديد الكل", "#95a5a6")
            deselect_all_btn.clicked.connect(self.deselect_all_records)

            # زر طباعة التوصيل الموحد
            print_unified_btn = self.create_styled_button("🖨️ طباعة التوصيل الموحد", "#e74c3c")
            print_unified_btn.clicked.connect(self.print_unified_receipt)

            controls_layout.addWidget(refresh_btn)
            controls_layout.addWidget(select_all_btn)
            controls_layout.addWidget(deselect_all_btn)
            controls_layout.addWidget(print_unified_btn)
            controls_layout.addStretch()

            layout.addWidget(controls_frame)

            # الجدول الموحد
            table_group = QGroupBox("📊 جميع السجلات")
            table_group.setFont(QFont("Calibri", 14, QFont.Bold))
            table_layout = QVBoxLayout(table_group)

            # إنشاء الجدول الموحد
            self.unified_table = QTableWidget()
            self.setup_unified_table()
            table_layout.addWidget(self.unified_table)

            layout.addWidget(table_group)



            print(f"✅ [SUCCESS] تم إنشاء التبويب الموحد بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التبويب الموحد: {str(e)}")
            traceback.print_exc()
            raise

    def setup_unified_table(self):
        """إعداد الجدول الموحد"""
        try:
            print(f"🔍 [DEBUG] إعداد الجدول الموحد...")

            columns = [
                "اختيار", "النوع", "ID", "الشهر", "السنة",
                "الشهري المطلوب", "الشهري المدفوع", "الشهري الباقي",
                "التسجيل المطلوب", "التسجيل المدفوع", "التسجيل الباقي",
                "حالة الدفع", "تاريخ الإضافة"
            ]

            self.unified_table.setColumnCount(len(columns))
            self.unified_table.setHorizontalHeaderLabels(columns)

            # تطبيق نمط مبسط للجدول (لا يتداخل مع مربعات الاختيار)
            self.unified_table.setStyleSheet("""
                QTableWidget {
                    background-color: #ffffff;
                    border: 1px solid #ddd;
                    gridline-color: #e0e0e0;
                }
                QHeaderView::section {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #1565C0, stop: 1 #0d47a1);
                    color: white;
                    border: none;
                    border-right: 1px solid #0d47a1;
                    font-family: 'Calibri';
                    font-size: 15px;
                    font-weight: bold;
                    text-align: center;
                    min-height: 30px;
                }
                QHeaderView::section:first {
                    border-top-left-radius: 5px;
                }
                QHeaderView::section:last {
                    border-top-right-radius: 5px;
                    border-right: none;
                }
            """)

            # إعدادات الجدول (بدون صفوف متناوبة)
            self.unified_table.setSelectionBehavior(QAbstractItemView.SelectItems)
            self.unified_table.setSelectionMode(QAbstractItemView.SingleSelection)
            self.unified_table.setSortingEnabled(True)

            # تطبيق الخط على رأس الجدول
            header = self.unified_table.horizontalHeader()
            header.setFont(QFont("Calibri", 13, QFont.Bold))
            header.setFixedHeight(45)
            header.setSectionResizeMode(QHeaderView.ResizeToContents)

            self.unified_table.verticalHeader().setDefaultSectionSize(40)
            self.unified_table.verticalHeader().setVisible(False)

            print(f"✅ [SUCCESS] تم إعداد الجدول الموحد بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد الجدول الموحد: {str(e)}")
            traceback.print_exc()
            raise

    def load_unified_data(self):
        """تحميل البيانات الموحدة من كلا الجدولين"""
        try:
            print(f"🔍 [DEBUG] تحميل البيانات الموحدة...")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على رمز التلميذ من البيانات المحملة
            student_code = self.student_data[2] if hasattr(self, 'student_data') and len(self.student_data) > 2 and self.student_data[2] else ""
            print(f"🔍 [DEBUG] تصفية البيانات الموحدة حسب رمز التلميذ: {student_code}")

            # جلب البيانات الموحدة من جدول monthly_duties مع أعمدة واجبات التسجيل
            if student_code:
                cursor.execute("""
                    SELECT 'أداء موحد' as type, id, month, year,
                           amount_required, amount_paid, amount_remaining,
                           مطلوب_التسجيل, مدفوع_التسجيل, باقي_التسجيل,
                           payment_status, created_date
                    FROM monthly_duties
                    WHERE الرمز = ?
                    ORDER BY year DESC,
                    CASE month
                        WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
                    END DESC
                """, (student_code,))
            else:
                # للتوافق مع البيانات القديمة
                cursor.execute("""
                    SELECT 'أداء موحد' as type, id, month, year,
                           amount_required, amount_paid, amount_remaining,
                           مطلوب_التسجيل, مدفوع_التسجيل, باقي_التسجيل,
                           payment_status, created_date
                    FROM monthly_duties
                    WHERE student_id = ?
                    ORDER BY year DESC,
                    CASE month
                        WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
                    END DESC
                """, (self.student_id,))

            all_records = cursor.fetchall()
            conn.close()

            print(f"🔍 [DEBUG] تم العثور على {len(all_records)} سجل أداء موحد")

            # تعيين عدد الصفوف
            self.unified_table.setRowCount(len(all_records))

            # ملء الجدول بالبيانات
            for row_index, record in enumerate(all_records):
                # مربع الاختيار المحسن (مثل تبويب إضافة الواجبات)
                checkbox_widget = self.create_enhanced_checkbox_unified(row_index)
                self.unified_table.setCellWidget(row_index, 0, checkbox_widget)

                # باقي البيانات - تحديث لتتناسب مع البنية الجديدة
                # تعريف عدد الأعمدة (13 عمود: اختيار + 12 عمود بيانات - بدون الملاحظات)
                total_columns = 13
                for col_index in range(1, total_columns):
                    if col_index == 1:  # النوع
                        value = record[0]
                        display_value = str(value)
                    elif col_index == 2:  # ID
                        value = record[1]
                        display_value = str(value)
                    elif col_index == 3:  # الشهر
                        value = record[2]
                        display_value = str(value)
                    elif col_index == 4:  # السنة
                        value = record[3]
                        display_value = str(value)
                    elif col_index in [5, 6, 7, 8, 9, 10]:  # الأعمدة المالية
                        value = record[col_index - 1] if col_index - 1 < len(record) else 0
                        display_value = f"{float(value):.2f} درهم" if value else "0.00 درهم"
                    elif col_index == 11:  # حالة الدفع
                        value = record[10] if len(record) > 10 else ""
                        display_value = str(value) if value else ""
                    elif col_index == 12:  # تاريخ الإضافة (بدون عمود الملاحظات)
                        value = record[11] if len(record) > 11 else ""
                        display_value = str(value) if value else ""
                    else:
                        display_value = ""

                    item = QTableWidgetItem(display_value)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 13, QFont.Bold))

                    # تلوين الأعمدة
                    if col_index == 1:  # النوع
                        item.setBackground(QColor("#e8f4fd"))
                        item.setForeground(QColor("#1565C0"))
                    elif col_index in [5, 6, 7]:  # أعمدة الواجبات الشهرية
                        item.setBackground(QColor("#e8f4fd"))  # أزرق فاتح
                        item.setForeground(QColor("#1565C0"))
                    elif col_index in [8, 9, 10]:  # أعمدة واجبات التسجيل
                        item.setBackground(QColor("#e8f5e8"))  # أخضر فاتح
                        item.setForeground(QColor("#2e7d32"))
                    elif col_index == 11:  # حالة الدفع
                        if "مدفوع كاملاً" in str(value):
                            item.setBackground(QColor("#d5f4e6"))
                            item.setForeground(QColor("#27ae60"))
                        elif "مدفوع جزئياً" in str(value):
                            item.setBackground(QColor("#fff3cd"))
                            item.setForeground(QColor("#856404"))
                        else:
                            item.setBackground(QColor("#f8d7da"))
                            item.setForeground(QColor("#721c24"))

                    self.unified_table.setItem(row_index, col_index, item)



            print(f"✅ [SUCCESS] تم تحميل البيانات الموحدة بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل البيانات الموحدة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات الموحدة: {str(e)}")

    def select_all_records(self):
        """تحديد جميع السجلات"""
        try:
            from PyQt5.QtWidgets import QCheckBox
            for row in range(self.unified_table.rowCount()):
                checkbox_widget = self.unified_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(True)
            print(f"✅ [SUCCESS] تم تحديد جميع السجلات")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديد جميع السجلات: {str(e)}")

    def deselect_all_records(self):
        """إلغاء تحديد جميع السجلات"""
        try:
            from PyQt5.QtWidgets import QCheckBox
            for row in range(self.unified_table.rowCount()):
                checkbox_widget = self.unified_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(False)
            print(f"✅ [SUCCESS] تم إلغاء تحديد جميع السجلات")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إلغاء تحديد جميع السجلات: {str(e)}")



    def select_all_registration_records(self):
        """تحديد جميع سجلات واجبات التسجيل"""
        try:
            from PyQt5.QtWidgets import QCheckBox
            for row in range(self.registration_table.rowCount()):
                checkbox_widget = self.registration_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(True)
            print(f"✅ [SUCCESS] تم تحديد جميع سجلات واجبات التسجيل")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديد جميع سجلات واجبات التسجيل: {str(e)}")

    def unselect_all_registration_records(self):
        """إلغاء تحديد جميع سجلات واجبات التسجيل"""
        try:
            from PyQt5.QtWidgets import QCheckBox
            for row in range(self.registration_table.rowCount()):
                checkbox_widget = self.registration_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(False)
            print(f"✅ [SUCCESS] تم إلغاء تحديد جميع سجلات واجبات التسجيل")
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إلغاء تحديد جميع سجلات واجبات التسجيل: {str(e)}")

    def delete_selected_registration_payments(self):
        """حذف دفعات واجبات التسجيل المحددة"""
        try:
            from PyQt5.QtWidgets import QCheckBox
            print(f"🔍 [DEBUG] بدء حذف دفعات واجبات التسجيل المحددة...")

            # جمع السجلات المحددة
            selected_rows = []
            for row in range(self.registration_table.rowCount()):
                checkbox_widget = self.registration_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        # الحصول على ID الدفعة من العمود 1 (بعد عمود الاختيار)
                        payment_id_item = self.registration_table.item(row, 1)
                        if payment_id_item:
                            payment_id = int(payment_id_item.text())
                            selected_rows.append((row, payment_id))

            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة واحدة على الأقل للحذف.")
                return

            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف {len(selected_rows)} دفعة محددة؟\nلا يمكن التراجع عن هذا الإجراء.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                deleted_count = 0
                for row, payment_id in selected_rows:
                    try:
                        cursor.execute("DELETE FROM registration_fees WHERE id = ?", (payment_id,))
                        deleted_count += 1
                        print(f"🔍 [DEBUG] تم حذف الدفعة ID: {payment_id}")
                    except Exception as e:
                        print(f"❌ [ERROR] خطأ في حذف الدفعة ID {payment_id}: {str(e)}")

                conn.commit()
                conn.close()

                print(f"✅ [SUCCESS] تم حذف {deleted_count} دفعة بنجاح")
                QMessageBox.information(self, "نجح", f"تم حذف {deleted_count} دفعة بنجاح.")

                # تحديث الجدول
                self.load_registration_data()
            else:
                print(f"🔍 [DEBUG] تم إلغاء عملية الحذف")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في حذف دفعات واجبات التسجيل المحددة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في حذف الدفعات المحددة: {str(e)}")

    def print_unified_receipt(self):
        """طباعة التوصيل الموحد للسجلات المحددة"""
        try:
            print(f"🔍 [DEBUG] بدء طباعة التوصيل الموحد...")

            # جمع السجلات المحددة
            selected_records = []
            for row in range(self.unified_table.rowCount()):
                checkbox_widget = self.unified_table.cellWidget(row, 0)
                if checkbox_widget:
                    from PyQt5.QtWidgets import QCheckBox
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        record_data = []
                        for col in range(1, self.unified_table.columnCount()):
                            item = self.unified_table.item(row, col)
                            record_data.append(item.text() if item else "")
                        selected_records.append(record_data)

            if not selected_records:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد سجل واحد على الأقل للطباعة.")
                return

            print(f"🔍 [DEBUG] تم تحديد {len(selected_records)} سجل للطباعة")

            # إنشاء محتوى التوصيل الموحد باستخدام نفس طريقة واجبات الأداء
            receipt_content = self.create_unified_receipt_content(selected_records)

            # طباعة التوصيل باستخدام الطابعة الحرارية
            self.send_to_thermal_printer(receipt_content)

            # إلغاء تحديد جميع السجلات بعد الطباعة الناجحة
            self.deselect_all_records()

            print(f"✅ [SUCCESS] تم إرسال التوصيل الموحد للطباعة بنجاح")
            QMessageBox.information(self, "نجح", "تم إرسال التوصيل الموحد للطباعة بنجاح.\nتم إلغاء تحديد جميع السجلات.")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة التوصيل الموحد: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التوصيل الموحد: {str(e)}")

    def create_unified_receipt_content(self, selected_records):
        """إنشاء محتوى التوصيل الموحد بنفس طريقة واجبات الأداء"""
        try:
            print(f"🔍 [DEBUG] إنشاء محتوى التوصيل الموحد...")

            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT المؤسسة, رقم_الهاتف, المدينة FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()
            conn.close()

            institution_name = institution_data[0] if institution_data and institution_data[0] else "المؤسسة التعليمية"
            institution_phone = institution_data[1] if institution_data and institution_data[1] else ""
            institution_city = institution_data[2] if institution_data and institution_data[2] else ""

            # بناء محتوى الوصل الموحد
            receipt_lines = []

            # إطار حول اسم المؤسسة والمدينة
            receipt_lines.append("=" * 40)
            receipt_lines.append(f"{institution_name}".center(40))
            if institution_city:
                receipt_lines.append(f"{institution_city}".center(40))
            receipt_lines.append("=" * 40)

            # الهاتف خارج الإطار
            if institution_phone:
                receipt_lines.append(f"هاتف: {institution_phone}".center(40))

            # عنوان التوصيل الموحد
            receipt_lines.append("وصل دفع موحد".center(40))

            # بيانات التلميذ
            if self.student_data:
                receipt_lines.append(f"اسم التلميذ: {self.student_data[1] or 'غير محدد'}".rjust(40))
                receipt_lines.append(f"رمز التلميذ: {self.student_data[2] or 'غير محدد'}".rjust(40))
                # إزالة "/" من القسم
                section_name = self.student_data[8] or 'غير محدد'
                if '/' in section_name:
                    section_name = section_name.replace('/', '')
                receipt_lines.append(f"القسم: {section_name}".rjust(40))

            receipt_lines.append("-" * 40)

            # تفاصيل السجلات المحددة
            total_monthly_amount = 0.0
            total_registration_amount = 0.0

            for i, record in enumerate(selected_records, 1):
                record_type = record[0]  # النوع
                record_id = record[1]    # ID

                if record_type == "أداء موحد":
                    month = record[2]  # الشهر
                    year = record[3]   # السنة

                    # الواجبات الشهرية
                    monthly_required = record[4]  # الشهري المطلوب
                    monthly_paid = record[5]      # الشهري المدفوع

                    # واجبات التسجيل
                    registration_required = record[7]  # التسجيل المطلوب
                    registration_paid = record[8]      # التسجيل المدفوع

                    payment_status = record[10]   # حالة الدفع

                    receipt_lines.append(f"الشهر: {month}".rjust(40))

                    # عرض الواجبات الشهرية إذا كانت أكبر من 0
                    try:
                        monthly_required_value = float(monthly_required.replace(" درهم", "")) if monthly_required else 0
                        monthly_paid_value = float(monthly_paid.replace(" درهم", "")) if monthly_paid else 0
                        monthly_remaining_value = monthly_required_value - monthly_paid_value

                        if monthly_required_value > 0:
                            receipt_lines.append("الواجبات الشهرية:".rjust(40))
                            receipt_lines.append(f"المطلوب: {monthly_required_value:.2f} درهم".rjust(40))
                            receipt_lines.append(f"المدفوع: {monthly_paid_value:.2f} درهم".rjust(40))
                            receipt_lines.append(f"الباقي: {monthly_remaining_value:.2f} درهم".rjust(40))
                            total_monthly_amount += monthly_paid_value
                    except:
                        pass

                    # عرض واجبات التسجيل إذا كانت أكبر من 0
                    try:
                        registration_required_value = float(registration_required.replace(" درهم", "")) if registration_required else 0
                        registration_paid_value = float(registration_paid.replace(" درهم", "")) if registration_paid else 0
                        registration_remaining_value = registration_required_value - registration_paid_value

                        if registration_required_value > 0:
                            receipt_lines.append("واجبات التسجيل:".rjust(40))
                            receipt_lines.append(f"المطلوب: {registration_required_value:.2f} درهم".rjust(40))
                            receipt_lines.append(f"المدفوع: {registration_paid_value:.2f} درهم".rjust(40))
                            receipt_lines.append(f"الباقي: {registration_remaining_value:.2f} درهم".rjust(40))
                            total_registration_amount += registration_paid_value
                    except:
                        pass

                    receipt_lines.append(f"حالة الدفع: {payment_status}".rjust(40))

                receipt_lines.append(f"رقم الوصل: {record_id}".rjust(40))

                if i < len(selected_records):  # خط فاصل بين السجلات
                    receipt_lines.append("-" * 20)

            # الإجماليات
            receipt_lines.append("-" * 40)
            receipt_lines.append("الإجماليات:".rjust(40))

            # عرض الإجماليات المنفصلة
            if total_monthly_amount > 0:
                receipt_lines.append(f"الواجبات الشهرية: {total_monthly_amount:.2f} درهم".rjust(40))

            if total_registration_amount > 0:
                receipt_lines.append(f"واجبات التسجيل: {total_registration_amount:.2f} درهم".rjust(40))

            total_amount = total_monthly_amount + total_registration_amount
            if total_amount > 0:
                receipt_lines.append(f"المبلغ الكلي: {total_amount:.2f} درهم".rjust(40))

            # تاريخ الطباعة
            from datetime import datetime
            receipt_lines.append(f"تاريخ الطباعة {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".rjust(40))
            receipt_lines.append("")  # سطر فارغ
            receipt_lines.append("نتمنى لكم التوفيق والنجاح".center(40))

            return "\n".join(receipt_lines)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء محتوى التوصيل الموحد: {str(e)}")
            traceback.print_exc()
            return "خطأ في إنشاء التوصيل الموحد"




# تشغيل التطبيق للاختبار
if __name__ == "__main__":
    print(f"🔍 [DEBUG] بدء تشغيل التطبيق للاختبار...")
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        window = MonthlyDutiesManagementWindow(student_id=1)
        window.show()
        print(f"✅ [SUCCESS] تم تشغيل التطبيق بنجاح")
    except Exception as e:
        print(f"❌ [ERROR] خطأ في تشغيل التطبيق: {str(e)}")
        traceback.print_exc()
    
    sys.exit(app.exec_())
